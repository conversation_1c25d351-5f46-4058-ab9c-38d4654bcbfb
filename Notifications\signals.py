from django.db.models.signals import post_save
from django.dispatch import receiver
from Notifications.models import Notification
from Templates.models import Template

@receiver(post_save, sender=Notification)
def update_template_full_definition_on_notification_change(sender, instance, **kwargs):
    for template in instance.templates.all():
        template.generate_full_definition()
        template.save()