from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from Projects.models import Project
from .utilFunctions import sync_project_users_on_save


@receiver(post_save, sender=Project)
def update_users_by_project_on_project_save(sender, instance, created, **kwargs):
    """
    Señal que se ejecuta cuando se guarda un proyecto.
    Sincroniza automáticamente la tabla UsersByProject con los datos del proyecto.
    """
    sync_project_users_on_save(instance)


@receiver(post_delete, sender=Project)
def delete_users_by_project_on_project_delete(sender, instance, **kwargs):
    """
    Señal que se ejecuta cuando se elimina un proyecto.
    Elimina automáticamente los registros relacionados en UsersByProject.
    """
    from .models import UsersByProject
    UsersByProject.objects.filter(project=instance).delete()
