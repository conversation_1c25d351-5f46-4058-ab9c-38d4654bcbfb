from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>

from .views import SubphaseFieldsSummaryView, SubphaseIncreaseOrderView, SubphaseDecreaseOrderView, MoveSubphaseView

urlpatterns = [
    path('list-subphases-fields/', SubphaseFieldsSummaryView.as_view(), name='list-subphases-fields'),
    path('increase-order/<int:subphase_id>/', SubphaseIncreaseOrderView.as_view(), name='increase-subphase-order'),
    path('decrease-order/<int:subphase_id>/', SubphaseDecreaseOrderView.as_view(), name='decrease-subphase-order'),
    path('move-subphase/', MoveSubphaseView.as_view(), name='move-subphase'),
]
