from django.urls import path
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>

from .views import UserViewSet, MeView, AssignUserRoleView, RoleViewSet, ListRecipients, sync_with_core, test_bbdd_connection


router = DefaultRouter()
router.register(r'roles', RoleViewSet, basename='roles')
router.register(r'crud', UserViewSet, basename='users')

urlpatterns = router.urls + [
    path('profile/', MeView.as_view(), name='me'),
    path('sync_with_core/', sync_with_core, name='sync_with_core'),
    path('test-bbdd/', test_bbdd_connection),
    path('assign-role/', AssignUserRoleView.as_view(), name='assign-role'),
    path('list-recipients/', ListRecipients.as_view(), name='list-recipients'),
]