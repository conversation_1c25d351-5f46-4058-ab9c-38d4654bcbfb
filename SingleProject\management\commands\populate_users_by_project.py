from django.core.management.base import BaseCommand
from SingleProject.utilFunctions import populate_users_by_project


class Command(BaseCommand):
    help = 'Migra los datos de usuarios del modelo Project a la tabla UsersByProject'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Ejecuta una simulación sin hacer cambios reales',
        )

    def handle(self, *args, **options):
        if options['dry_run']:
            self.stdout.write(
                self.style.WARNING('🔄 Ejecutando en modo DRY RUN - No se harán cambios reales')
            )
            # Aquí podrías implementar lógica de dry run si fuera necesario
            return

        self.stdout.write(
            self.style.SUCCESS('🚀 Iniciando migración de usuarios por proyecto...')
        )
        
        try:
            created_count = populate_users_by_project()
            self.stdout.write(
                self.style.SUCCESS(
                    f'✅ Migración completada exitosamente. {created_count} registros creados.'
                )
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Error durante la migración: {str(e)}')
            )
            raise e
