from django.db.models.signals import post_migrate
from django.dispatch import receiver
from Projects.models import DateField, ImplementationTypes, TypeOfUsers
from Fields.models import Phase

@receiver(post_migrate)
def create_project_date_fields(sender, **kwargs):
    if sender.name != "Projects":
        return

    # Get all required phases after migration
    try:
        start_phase = Phase.objects.get(name="START")
        collection_phase = Phase.objects.get(name="COLLECTION")
        migration_phase = Phase.objects.get(name="MIGRATION")
        test_phase = Phase.objects.get(name="TEST")
        golive_phase = Phase.objects.get(name="GO LIVE")
        incubadora_phase = Phase.objects.get(name="INCUBADORA")
    except Phase.DoesNotExist:
        print("❌ Faltan fases requeridas. Asegúrate de que las fases existen antes de crear los DateFields.")
        return

    date_fields = [
        ("start_initial_date", False, start_phase),
        ("start_final_date", False, start_phase),
        ("start_real_initial_date", False, start_phase),
        ("start_real_final_date", False, start_phase),
        ("collection_initial_date", False, collection_phase),
        ("collection_final_date", False, collection_phase),
        ("collection_real_initial_date", False, collection_phase),
        ("collection_real_final_date", False, collection_phase),
        ("migration_initial_date", False, migration_phase),
        ("migration_final_date", False, migration_phase),
        ("migration_real_initial_date", False, migration_phase),
        ("migration_real_final_date", False, migration_phase),
        ("test_initial_date", False, test_phase),
        ("test_final_date", False, test_phase),
        ("test_real_initial_date", False, test_phase),
        ("test_real_final_date", False, test_phase),
        ("month1_test", True, test_phase),
        ("month2_test", True, test_phase),
        ("golive_initial_date", False, golive_phase),
        ("golive_final_date", False, golive_phase),
        ("golive_real_initial_date", False, golive_phase),
        ("golive_real_final_date", False, golive_phase),
        ("incubadora_initial_date", False, incubadora_phase),
        ("incubadora_final_date", False, incubadora_phase),
        ("incubadora_real_initial_date", False, incubadora_phase),
        ("incubadora_real_final_date", False, incubadora_phase),
    ]

    DateField.objects.all().delete()
    for name, is_month, phase in date_fields:
        DateField.objects.get_or_create(name=name, defaults={"is_month_year": is_month, "phase": phase})

    print("✅ Campos `DateField` creados automáticamente tras migrate en Projects.")



@receiver(post_migrate)
def ensure_implementation_types(sender, **kwargs):
    # Solo si la migración afecta a la app Projects
    if sender.name != "Projects":
        return

    default_types = ["Nueva", "Existente", "Migración", "Subrogación"]

    for type_name in default_types:
        ImplementationTypes.objects.get_or_create(name=type_name)

    print("✅ Campos ImplementationType insertados/verificados correctamente.")


@receiver(post_migrate)
def ensure_type_of_users(sender, **kwargs):
    # Solo si la migración afecta a la app Projects
    if sender.name != "Projects":
        return

    default_user_types = ["Coordinador", "Implementador", "Implementador 2", "Backup Implementador", "Incubadora"]

    for user_type in default_user_types:
        TypeOfUsers.objects.get_or_create(type_of_user=user_type)

    print("✅ Campos TypeOfUsers insertados/verificados correctamente.")
