from Users.core_db import create_query_rosclar
from Users.models import RMOUser
from Team.models import Team
from django.db import transaction

def get_all_users_core():
    q = """
        SELECT * FROM (
            SELECT
                t.name_alias,
                t.email,
                tg.name_alias AS group_name_alias,
                tg.code,
                tga.is_leader,
                ROW_NUMBER() OVER (PARTITION BY t.email ORDER BY t.email)  rn
            FROM
                brain_event_store_prod.technicians t
            JOIN
                brain_event_store_prod.technician_group_assignations tga ON tga.technician_id = t.id
            JOIN
                brain_event_store_prod.technician_groups tg ON tga.group_id = tg.id
            WHERE tg.code IS NOT NULL AND tg.code <> "LEG" AND tg.code <> "ADM"
        ) a
        WHERE rn = 1
    """
    return create_query_rosclar(q, db_name="core")

@transaction.atomic 
def split_full_name(full_name):
    parts = full_name.strip().split()
    if len(parts) == 1:
        return parts[0], ''
    return parts[0], ' '.join(parts[1:])


def import_users_from_core():
    rows = get_all_users_core()
    created_count = 0
    updated_count = 0

    for name, email, group_name, group_code, is_leader, rn in rows:
        first_name, last_name = split_full_name(name)

        team, created = Team.objects.get_or_create(
            code=f"{group_name}_{group_code}",
            defaults={'name': group_name}
        )
        if not created and team.name != group_name:
            team.name = group_name
            team.save()

        team_code_str = f"{group_name} ({group_code})"

        # Generar contraseña inicial
        initial_password = f"{first_name}1234"

        user, user_created = RMOUser.objects.update_or_create(
            email=email,
            defaults={
                "first_name": first_name,
                "last_name": last_name,
                "name": name,
                "team": team,
                "team_code": team_code_str,
                "position": bool(is_leader)
            }
        )

        if user_created:
            user.set_password(initial_password)
            user.must_change_password = True
            user.save()
            print(f"🔑 Usuario creado: {user.email} → contraseña inicial: {initial_password}")
            created_count += 1
        else:
            updated_count += 1

        action = "Creado" if user_created else "Actualizado"
        # print(f"{action}: {user.email} → {first_name} {last_name}")

    print("\n✅ Importación completada")
    print(f"👥 Usuarios creados: {created_count}")
    print(f"🔄 Usuarios actualizados: {updated_count}")
    print(f"📊 Total procesados: {created_count + updated_count}")


def split_full_name(full_name):
    parts = full_name.strip().split()
    if len(parts) == 1:
        return parts[0], ''
    return parts[0], ' '.join(parts[1:])


