# Generated by Django 3.2.25 on 2025-05-30 08:36

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Team', '__first__'),
    ]

    operations = [
        migrations.CreateModel(
            name='DateField',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('is_month_year', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='ImplementationTypes',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lid', models.CharField(max_length=50, unique=True)),
                ('alias', models.CharField(max_length=50, unique=True)),
                ('company_name', models.CharField(max_length=255)),
                ('aggregator', models.CharField(blank=True, max_length=255, null=True)),
                ('implementation_type', models.CharField(blank=True, max_length=255, null=True)),
                ('template', models.JSONField(blank=True, null=True)),
                ('start_initial_date', models.DateField(blank=True, null=True)),
                ('start_final_date', models.DateField(blank=True, null=True)),
                ('collection_initial_date', models.DateField(blank=True, null=True)),
                ('collection_final_date', models.DateField(blank=True, null=True)),
                ('migration_initial_date', models.DateField(blank=True, null=True)),
                ('migration_final_date', models.DateField(blank=True, null=True)),
                ('test_initial_date', models.DateField(blank=True, null=True)),
                ('test_final_date', models.DateField(blank=True, null=True)),
                ('month1_test', models.DateField(blank=True, null=True)),
                ('month2_test', models.DateField(blank=True, null=True)),
                ('golive_initial_date', models.DateField(blank=True, null=True)),
                ('golive_final_date', models.DateField(blank=True, null=True)),
                ('incubadora_initial_date', models.DateField(blank=True, null=True)),
                ('incubadora_final_date', models.DateField(blank=True, null=True)),
                ('backup', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='backup_projects', to=settings.AUTH_USER_MODEL)),
                ('coordinator', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='coordinated_projects1', to=settings.AUTH_USER_MODEL)),
                ('implementer1', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='implemented_projects_1', to=settings.AUTH_USER_MODEL)),
                ('implementer2', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='implemented_projects_2', to=settings.AUTH_USER_MODEL)),
                ('incubator', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='incubated_projects', to=settings.AUTH_USER_MODEL)),
                ('team', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='Team.team')),
            ],
        ),
        migrations.CreateModel(
            name='TimestampPrediction',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value_date', models.DateField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('is_latest', models.BooleanField(default=True)),
                ('field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Projects.datefield')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='timestamps', to='Projects.project')),
            ],
        ),
    ]
