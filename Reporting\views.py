import os
from django.shortcuts import render
from django.template.loader import render_to_string
from rest_framework.views import APIView
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.conf import settings
from django.http import HttpResponse
from django.utils import timezone
from weasyprint import HTML, CSS
import pdfkit
import json

from .utils import get_image_as_base64, read_css_file
from Users.utilFunctions import permissions_required
from .models import Report
from Projects.models import Project
from .serializers import ReportSerializer
from SingleProject.models import FieldsByProject, PhasesByProject
from SingleProject.utilFunctions import compute_phase_percentage

class ReportListView(APIView):
    """
    Endpoint para obtener una lista de todos los reportes.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_config')]
    @swagger_auto_schema(
        operation_summary="Obtener todos los reportes",
        responses={
            200: openapi.Response(
                description="Lista de reportes",
            )
        }
    )
    def get(self, request):
        reports = Report.objects.all()
        data = []
        for report in reports:
            fields_data = [
                {"id": field.id, "name": field.name}
                for field in report.fields.all()
            ]
            data.append({
                "id": report.id,
                "name": report.name,
                "description": report.description,
                "document_name": report.document_name,
                "observations": report.observations,
                # "use_english": report.use_english,
                "filters": report.filters,
                "active": report.active,
                "phases": report.phases,
                "subphases": report.subphases,
                "created_at": report.created_at,
                "fields": fields_data,
            })
        return Response(data)


class ReportFilteredListView(APIView):
    """
    Endpoint para obtener una lista de todos los reportes activos (active = True)
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_config')]
    @swagger_auto_schema(
        operation_summary="Obtener todos los reportes activos",
        responses={
            200: openapi.Response(
                description="Lista de reportes activos",
            )
        }
    )
    def get(self, request):
        reports = Report.objects.filter(active=True)
        data = []
        for report in reports:
            fields_data = [
                {"id": field.id, "name": field.name}
                for field in report.fields.all()
            ]
            data.append({
                "id": report.id,
                "name": report.name,
                "description": report.description,
                "document_name": report.document_name,
                "observations": report.observations,
                # "use_english": report.use_english,
                "filters": report.filters,
                "active": report.active,
                "phases": report.phases,
                "subphases": report.subphases,
                "created_at": report.created_at,
                "fields": fields_data,
            })
        return Response(data)


class ReportDetailView(APIView):
    """
    Endpoint para obtener los detalles de un reporte específico.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_config')]
    @swagger_auto_schema(
        operation_summary="Obtener un reporte por ID",
        manual_parameters=[
            openapi.Parameter('id', openapi.IN_PATH, description="ID del reporte", type=openapi.TYPE_INTEGER, required=True)
        ],
        responses={
            200: openapi.Response(
                description="Detalles del reporte",
                schema=ReportSerializer()
            ),
            404: "Reporte no encontrado"
        }
    )
    def get(self, request, id):
        try:
            report = Report.objects.get(id=id)
        except Report.DoesNotExist:
            return Response({"error": "Reporte no encontrado."}, status=status.HTTP_404_NOT_FOUND)
        
        serializer = ReportSerializer(report)
        return Response(serializer.data)


class ReportCreateView(APIView):
    """
    Endpoint para crear un nuevo reporte.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.editar_config')]
    @swagger_auto_schema(
        operation_summary="Crear un nuevo reporte",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "reportConfig": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        "name": openapi.Schema(type=openapi.TYPE_STRING, description="Nombre del reporte"),
                        "name_en": openapi.Schema(type=openapi.TYPE_STRING, description="Nombre del reporte en inglés"),
                        "description": openapi.Schema(type=openapi.TYPE_STRING, description="Descripción"),
                        "description_en": openapi.Schema(type=openapi.TYPE_STRING, description="Descripción en inglés"),
                        "documentName": openapi.Schema(type=openapi.TYPE_STRING, description="Nombre del documento"),
                        "useEnglishFields": openapi.Schema(type=openapi.TYPE_BOOLEAN, description="Usar campos en inglés"),
                        "includeObservations": openapi.Schema(type=openapi.TYPE_BOOLEAN, description="Incluir observaciones"),
                        "phases": openapi.Schema(type=openapi.TYPE_BOOLEAN, description="Incluir fases"),
                        "subphases": openapi.Schema(type=openapi.TYPE_BOOLEAN, description="Incluir subfases"),
                    },
                    required=["name", "name_en", "description", "description_en", "documentName", "useEnglishFields", "includeObservations"]
                ),
                "selectedFields": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_STRING),
                    description="Lista de IDs de campos seleccionados"
                ),
                "fieldFilters": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description="Filtros de campos por tipo"
                ),
            },
            required=["reportConfig", "selectedFields", "fieldFilters"]
        ),
        responses={
            201: openapi.Response(
                description="Reporte creado correctamente",
                schema=ReportSerializer()
            ),
            400: "Datos inválidos"
        }
    )
    def post(self, request):
        data = request.data
        config = data.get('reportConfig', {})
        selected_fields = data.get('selectedFields', [])
        field_filters = data.get('fieldFilters', {})

        report = Report.objects.create(
            name=config.get('name'),
            name_en=config.get('name_en'),
            description=config.get('description'),
            description_en=config.get('description_en'),
            document_name=config.get('documentName'),
            use_english=config.get('useEnglishFields', False),
            observations=config.get('includeObservations', False),
            phases=config.get('phases', False),
            subphases=config.get('subphases', False),
            filters=field_filters
        )
        # Convert selected_fields to integers if needed
        field_ids = [int(fid) for fid in selected_fields]
        report.fields.set(field_ids)
        return Response(ReportSerializer(report).data, status=status.HTTP_201_CREATED)


class ReportUpdateView(APIView):
    """
    Endpoint para modificar un reporte existente.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.editar_config')]
    @swagger_auto_schema(
        operation_summary="Modificar un reporte existente",
        request_body=ReportSerializer,
        manual_parameters=[
            openapi.Parameter('id', openapi.IN_PATH, description="ID del reporte", type=openapi.TYPE_INTEGER, required=True)
        ],
        responses={
            200: openapi.Response(
                description="Reporte modificado correctamente",
                schema=ReportSerializer()
            ),
            400: "Datos inválidos",
            404: "Reporte no encontrado"
        }
    )
    def put(self, request, id):
        try:
            report = Report.objects.get(id=id)
        except Report.DoesNotExist:
            return Response({"error": "Reporte no encontrado."}, status=status.HTTP_404_NOT_FOUND)
        serializer = ReportSerializer(report, data=request.data, partial=True)
        if serializer.is_valid():
            report = serializer.save()
            field_ids = request.data.get('fields')
            if field_ids is not None:
                report.fields.set(field_ids)
            return Response(ReportSerializer(report).data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ReportDeleteView(APIView):
    """
    Endpoint para eliminar un reporte existente.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.editar_config')]
    @swagger_auto_schema(
        operation_summary="Eliminar un reporte existente",
        manual_parameters=[
            openapi.Parameter('id', openapi.IN_PATH, description="ID del reporte", type=openapi.TYPE_INTEGER, required=True)
        ],
        responses={
            204: "Reporte eliminado correctamente",
            404: "Reporte no encontrado"
        }
    )
    def delete(self, request, id):
        try:
            report = Report.objects.get(id=id)
        except Report.DoesNotExist:
            return Response({"error": "Reporte no encontrado."}, status=status.HTTP_404_NOT_FOUND)
        report.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class ReportToggleView(APIView):
    """
    Endpoint para activar o desactivar un reporte.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.editar_config')]
    @swagger_auto_schema(
        operation_summary="Activar o desactivar un reporte",
        manual_parameters=[
            openapi.Parameter('id', openapi.IN_PATH, description="ID del reporte", type=openapi.TYPE_INTEGER, required=True)
        ],
        responses={
            200: openapi.Response(
                description="Reporte activado/desactivado correctamente",
                schema=ReportSerializer()
            ),
            404: "Reporte no encontrado"
        }
    )
    def post(self, request, id):
        try:
            report = Report.objects.get(id=id)
        except Report.DoesNotExist:
            return Response({"error": "Reporte no encontrado."}, status=status.HTTP_404_NOT_FOUND)
        
        report.active = not report.active
        report.save()
        return Response(ReportSerializer(report).data)
    

class GenerateProjectPlanPDFView(APIView):
    """
    Endpoint para generar un PDF con la información del proyecto (Project Plan).
    Genera un reporte en formato PDF con los detalles del proyecto, 
    incluyendo información sobre las fases, campos y progreso.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.editar_config')]

    @swagger_auto_schema(
        operation_summary="Generar PDF del plan de proyecto",
        manual_parameters=[
            openapi.Parameter(
                'project_id', 
                openapi.IN_PATH, 
                description="ID del proyecto", 
                type=openapi.TYPE_INTEGER, 
                required=True
            ),
            openapi.Parameter(
                'language', 
                openapi.IN_QUERY, 
                description="Idioma del reporte (es o en)", 
                type=openapi.TYPE_STRING, 
                required=False,
                enum=['es', 'en']
            )
        ],
        responses={
            200: openapi.Response(description="PDF generado exitosamente"),
            400: "Datos inválidos o error en la generación",
            404: "Proyecto no encontrado",
            500: "Error interno del servidor"
        }
    )
    def get(self, request, project_id, *args, **kwargs):
        try:
            # Get language parameter (default to 'es')
            language = request.GET.get('language', 'es')
            if language not in ['es', 'en']:
                return Response({"error": "Idioma no válido. Use 'es' o 'en'"}, status=status.HTTP_400_BAD_REQUEST)
            
            # Get project data
            try:
                project = Project.objects.get(id=project_id)
            except Project.DoesNotExist:
                return Response({"error": "Proyecto no encontrado"}, status=status.HTTP_404_NOT_FOUND)

            # Build context for the template
            context = self._build_context(project)

            # Select template based on language
            template_name = f'project_plan_{language}.html'

            # Filename
            if language == 'en':
                filename = f"{timezone.now().strftime('%y%m%d')}_{project.lid}_{project.company_name}_ProjectPlan_EN.pdf"
            else:
                filename = f"{timezone.now().strftime('%y%m%d')}_{project.lid}_{project.company_name}_PlanDeProyecto_ES.pdf"
            
            context['filename'] = filename

            # Render template to HTML string
            html_string = render_to_string(template_name, context)
            
            # Set up PDF configuration
            try:
                if os.path.exists(r'C:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe'):
                    pdfkit_path = pdfkit.configuration(wkhtmltopdf=r'C:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe')
                else:
                    pdfkit_path = pdfkit.configuration()
                config = pdfkit_path
                css_path = os.path.join(settings.BASE_DIR, 'static', 'css', 'styles_pp.css')
                
                options = {
                    'enable-local-file-access': '',
                    'encoding': 'UTF-8',
                    # Page size and orientation
                    'page-size': 'A4',
                    'orientation': 'Landscape',  # Change to 'Portrait' if needed
                    'margin-top': '0.05in',
                    'margin-right': '0.05in',
                    'margin-bottom': '0.05in',
                    'margin-left': '0.05in',
                    # Disable smart width/height to respect CSS dimensions
                    'disable-smart-shrinking': '',
                    # Print options
                    'print-media-type': '',
                    'quiet': '',
                    # Scale content to fit better
                    'zoom': '0.95',
                }
                
                # Generate PDF
                try:
                    pdf_output = pdfkit.from_string(
                        html_string,
                        False,
                        configuration=config,
                        options=options,
                        css=css_path
                    )
                except Exception as e:
                    with open('debug_output.txt', 'w') as f:
                        f.write(f"PDF Generation Error: {str(e)}")
                    return Response({"error": f"Error al generar el PDF: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

                # Set response
                response = HttpResponse(pdf_output, content_type='application/pdf')
                response['Content-Disposition'] = f'attachment; filename="{filename}"'
                return response
                
            except Exception as e:
                return Response({"error": f"Error al generar el PDF: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
        except Exception as e:
            return Response({"error": f"Error inesperado: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _build_context(self, project):
        # Get phases by project to calculate progress
        phases_by_project = PhasesByProject.objects.filter(project=project).order_by('phase__order')
                
        # Calculate milestones percentages
        percentages = []
        for phase_by_project in phases_by_project:
            phase = phase_by_project.phase
            all_milestone_fields = FieldsByProject.objects.filter(
                project=project, 
                phase=phase, 
                field__is_milestone=True, 
                active=True
            ).count()
            completed_milestone_fields = FieldsByProject.objects.filter(
                project=project, 
                phase=phase, 
                field__is_milestone=True, 
                active=True,
                status=FieldsByProject.COMPLETED
            ).count()
            percentage = completed_milestone_fields / all_milestone_fields * 100 if all_milestone_fields > 0 else 0

            percentages.append({
                'id': phase.id,
                'name': phase.name,
                'percentage': int(percentage),
                'count': f"{completed_milestone_fields}/{all_milestone_fields}"
            })
            
        # Build the complete context
        static_dir = os.path.join(settings.BASE_DIR, 'static')
        context = {
            'project': project,
            'percentages': percentages,
            "css_path": f"file:///{static_dir}/css/styles_pp.css".replace('\\', '/'),
            "logo_path": f"file:///{static_dir}/img/logo.png".replace('\\', '/'),
            "favicon_path": f"file:///{static_dir}/img/favicon.png".replace('\\', '/'),
        }
        return context


# class GenerateReportingPDFView(APIView):
#     """
#     Endpoint para generar un PDF de un reporte.
#     Genera un reporte en formato PDF con los detalles del proyecto, 
#     incluyendo información sobre las fases, subfases y campos.
#     """
#     permission_classes = [IsAuthenticated, permissions_required('Users.editar_config')]

#     @swagger_auto_schema(
#         operation_summary="Generar PDF del plan de proyecto",
#         manual_parameters=[
#             openapi.Parameter(
#                 'project_id', 
#                 openapi.IN_PATH, 
#                 description="ID del proyecto", 
#                 type=openapi.TYPE_INTEGER, 
#                 required=True
#             ),
#             openapi.Parameter(
#                 'language', 
#                 openapi.IN_QUERY, 
#                 description="Idioma del reporte (es o en)", 
#                 type=openapi.TYPE_STRING, 
#                 required=True,
#                 enum=['es', 'en']
#             ),
#             openapi.Parameter(
#                 'report_id',
#                 openapi.IN_QUERY,
#                 description="ID del reporte",
#                 type=openapi.TYPE_INTEGER,
#                 required=True
#             ),
#         ],
#         responses={
#             200: openapi.Response(description="PDF generado exitosamente"),
#             400: "Datos inválidos o error en la generación",
#             404: "Proyecto no encontrado",
#             500: "Error interno del servidor"
#         }
#     )
#     def get(self, request, project_id, *args, **kwargs):
#         try:
#             # Get language parameter (default to 'es')
#             language = request.GET.get('language', 'es')
#             if language not in ['es', 'en']:
#                 return Response({"error": "Idioma no válido. Use 'es' o 'en'"}, status=status.HTTP_400_BAD_REQUEST)
            
#             # Get project data
#             try:
#                 project = Project.objects.get(id=project_id)
#             except Project.DoesNotExist:
#                 return Response({"error": "Proyecto no encontrado"}, status=status.HTTP_404_NOT_FOUND)

#             # Get report ID
#             try:
#                 report = Report.objects.get(id=request.GET.get('report_id'))
#             except Report.DoesNotExist:
#                 return Response({"error": "Reporte no encontrado"}, status=status.HTTP_404_NOT_FOUND)

#             # Get fields that are both in the report and exist in FieldsByProject for this project
#             report_fields = report.fields.all()
#             fields_by_project = FieldsByProject.objects.filter(
#                 project=project,
#                 field__in=report_fields,
#                 active=True
#             ).select_related('field', 'phase')

#             # Build context for the template
#             context = self._build_context(project, report, fields_by_project, language)

#             # Select template based on language
#             template_name = f'reporting_{language}.html'

#             if language == 'en':
#                 filename = f"{timezone.now().strftime('%y%m%d')}_{project.lid}_{project.company_name}_{report.name_en}_EN.pdf"
#             else:
#                 filename = f"{timezone.now().strftime('%y%m%d')}_{project.lid}_{project.company_name}_{report.name}_ES.pdf"

#             context['filename'] = filename
#             context['header_img_path'] = f"file:///{os.path.join(settings.BASE_DIR, 'static', 'img', 'header_generated.png')}".replace('\\', '/')

#             # Render template to HTML string
#             html_string = render_to_string(template_name, context)
            
#             header_context = {
#                 "logo_path": "file:///C:/Users/<USER>/VSCodeProjects/Rosclar_RMO_All/Rosclar_RMO/static/img/logo.png",
#                 "css_path": "file:///C:/Users/<USER>/VSCodeProjects/Rosclar_RMO_All/Rosclar_RMO/static/css/styles_rep.css",
#                 # Add other variables as needed
#             }
#             header_html = render_to_string('header.html', header_context)
#             header_html_path = os.path.join(settings.BASE_DIR, 'Reporting', 'templates', 'header_rendered.html')
#             with open(header_html_path, 'w', encoding='utf-8') as f:
#                 f.write(header_html)

#             print(f"Header HTML written to {header_html_path}")

#             # Set up PDF configuration
#             try:
#                 config = pdfkit.configuration(wkhtmltopdf=r'C:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe')
#                 css_path = os.path.join(settings.BASE_DIR, 'static', 'css', 'styles_rep.css')
                
#                 options = {
#                     'enable-local-file-access': '',
#                     'encoding': 'UTF-8',
#                     'page-width': '210mm',
#                     'page-height': '297mm',
#                     'orientation': 'Landscape',  # Change to 'Portrait' if needed
#                     'margin-top': '0.015in',
#                     'margin-right': '0.05in',
#                     'margin-bottom': '0.05in',
#                     'margin-left': '0.05in',
#                     'disable-smart-shrinking': '',
#                     'print-media-type': '',
#                     # 'quiet': '',
#                     'header-html': header_html_path,
#                 }
                
#                 # Generate PDF
#                 try:
#                     pdf_output = pdfkit.from_string(
#                         html_string,
#                         False,
#                         configuration=config,
#                         options=options,
#                         css=css_path
#                     )
#                 except Exception as e:
#                     # Run wkhtmltopdf manually for more verbose output
#                     import subprocess
#                     wkhtmltopdf_bin = config.wkhtmltopdf if hasattr(config, 'wkhtmltopdf') else r'C:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe'
#                     html_file = 'debug_output.html'
#                     pdf_file = 'debug_output.pdf'
#                     # Write the HTML to a file for manual testing
#                     with open(html_file, 'w', encoding='utf-8') as f:
#                         f.write(html_string)
#                     # Build the command (do NOT pass CSS as a positional argument)
#                     cmd = [
#                         wkhtmltopdf_bin,
#                         '--enable-local-file-access',
#                         '--encoding', 'UTF-8',
#                         '--margin-top', '0.05in',
#                         '--margin-right', '0.05in',
#                         '--margin-bottom', '0.05in',
#                         '--margin-left', '0.05in',
#                         '--page-width', '210mm',
#                         '--page-height', '297mm',
#                         '--orientation', 'Landscape',
#                         '--disable-smart-shrinking',
#                         '--print-media-type',
#                         html_file,
#                         pdf_file
#                     ]
#                     # Add quiet for less noise, remove if you want even more output
#                     # cmd.append('--quiet')
#                     # Add CSS as user style sheet if needed (not as positional argument)
#                     # cmd.extend(['--user-style-sheet', css_path])
#                     try:
#                         result = subprocess.run(cmd, capture_output=True, text=True)
#                         with open('debug_output.txt', 'w', encoding='utf-8') as f:
#                             f.write(f"PDF Generation Error: {str(e)}\n")
#                             f.write(f"Manual wkhtmltopdf call stdout:\n{result.stdout}\n")
#                             f.write(f"Manual wkhtmltopdf call stderr:\n{result.stderr}\n")
#                         error_msg = f"PDF Generation Error: {str(e)}\nManual wkhtmltopdf call stderr:\n{result.stderr}"
#                     except Exception as sube:
#                         error_msg = f"PDF Generation Error: {str(e)}\nManual wkhtmltopdf call failed: {str(sube)}"
#                     return Response({"error": f"Error al generar el PDF: {error_msg}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

#                 # Set response
#                 response = HttpResponse(pdf_output, content_type='application/pdf')
#                 response['Content-Disposition'] = f'attachment; filename="{filename}"'
#                 return response
                
#             except Exception as e:
#                 return Response({"error": f"Error al generar el PDF: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                
#         except Exception as e:
#             return Response({"error": f"Error inesperado: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    # def _build_context(self, project, report, fields_by_project, language):
    #     # Filter fields_by_project based on the report filters
    #     filtered_fields = fields_by_project
        
    #     if report.filters:
    #         # Apply filters based on field type and status
    #         filtered_fields = []
    #         for field_by_project in fields_by_project:
    #             include_field = False
    #             field_type = getattr(field_by_project.field, 'type', None)
    #             field_status = field_by_project.status.lower() if field_by_project.status else None
    #             # Check if this field matches any filter criteria
    #             if field_type in report.filters:
    #                 type_filters = report.filters[field_type]
    #                 allowed_statuses = type_filters.get('statuses', [])
    #                 if field_status in allowed_statuses:
    #                     include_field = True
    #             if include_field:
    #                 filtered_fields.append(field_by_project)
        

    #     def expand_task_with_subtasks(field_by_project, language):
    #         """
    #         Expands a TASK_WITH_SUBTASKS field into multiple row objects
    #         """
    #         rows = []
            
    #         # Main task row (parent)
    #         main_row = {
    #             'is_subtask': False,
    #             'field_name': field_by_project.field.name,
    #             'field_type': field_by_project.field.type,
    #             'status': field_by_project.status,
    #             'value': '',  # Empty value for main task
    #             'observations': getattr(field_by_project, 'observations', ''),
    #             'original_field': field_by_project.field
    #         }
    #         rows.append(main_row)
            
    #         # Subtask rows
    #         try:
    #             import json
    #             subtasks_data = field_by_project.field.subtasks
    #             if isinstance(subtasks_data, list):
    #                 subtasks = subtasks_data
    #             else:
    #                 subtasks = json.loads(subtasks_data)
                
    #             # Get values for subtasks
    #             value = getattr(field_by_project, 'value', '')
    #             if isinstance(value, str):
    #                 try:
    #                     values = json.loads(value)
    #                     if not isinstance(values, list):
    #                         values = [values]
    #                 except Exception:
    #                     values = [value] * len(subtasks)
    #             elif isinstance(value, list):
    #                 values = value
    #             else:
    #                 values = [value] * len(subtasks)
                
    #             # Create subtask rows
    #             for i, subtask in enumerate(subtasks):
    #                 subtask_status = values[i] if i < len(values) else ''

    #                 match subtask_status:
    #                     case 'completado':
    #                         subtask_status = 'COMPLETED'
    #                     case 'en_progreso':
    #                         subtask_status = 'IN_PROGRESS'
    #                     case 'no_empezado':
    #                         subtask_status = 'NOT_STARTED'

    #                 subtask_row = {
    #                     'is_subtask': True,
    #                     'field_name': subtask.get('title', ''),
    #                     'field_type': field_by_project.field.type,
    #                     'status': subtask_status,
    #                     'value': subtask_status,
    #                     'observations': '',
    #                     'original_field': field_by_project.field
    #                 }
    #                 rows.append(subtask_row)
                    
    #         except Exception as e:
    #             print(f"Error processing subtasks: {e}")
            
    #         return rows

    #     # Build organized data structure for template
    #     organized_data = {}
        
    #     if report.phases:
    #         # Group by phases
    #         for field_by_project in filtered_fields:
    #             phase = field_by_project.phase
    #             if phase:
    #                 phase_id = phase.id
    #                 if phase_id not in organized_data:
    #                     organized_data[phase_id] = {
    #                         'phase': phase,
    #                         'subphases': {} if report.subphases else None,
    #                         'fields': [] if not report.subphases else None
    #                     }
                    
    #                 if report.subphases and field_by_project.field.subphase:
    #                     # Group by subphases within phases
    #                     subphase = field_by_project.field.subphase
    #                     subphase_id = subphase.id
    #                     if subphase_id not in organized_data[phase_id]['subphases']:
    #                         organized_data[phase_id]['subphases'][subphase_id] = {
    #                             'subphase': subphase,
    #                             'fields': []
    #                         }
                        
    #                     # Process field based on type
    #                     if field_by_project.field.type == "TASK_WITH_SUBTASKS" and field_by_project.field.subtasks:
    #                         # Expand into multiple rows
    #                         expanded_rows = expand_task_with_subtasks(field_by_project, language)
    #                         organized_data[phase_id]['subphases'][subphase_id]['fields'].extend(expanded_rows)
    #                     elif field_by_project.field.type == "TASK":
    #                         # Single row with formatted value
    #                         formatted_value = self.format_task_value(field_by_project.value, language)
    #                         row = {
    #                             'is_subtask': False,
    #                             'field_name': field_by_project.field.name,
    #                             'field_type': field_by_project.field.type,
    #                             'status': field_by_project.status,
    #                             'value': formatted_value,
    #                             'observations': getattr(field_by_project, 'observations', ''),
    #                             'original_field': field_by_project.field
    #                         }
    #                         organized_data[phase_id]['subphases'][subphase_id]['fields'].append(row)
    #                     else:
    #                         # Other field types
    #                         row = {
    #                             'is_subtask': False,
    #                             'field_name': field_by_project.field.name,
    #                             'field_type': field_by_project.field.type,
    #                             'status': field_by_project.status,
    #                             'value': field_by_project.value,
    #                             'observations': getattr(field_by_project, 'observations', ''),
    #                             'original_field': field_by_project.field
    #                         }
    #                         organized_data[phase_id]['subphases'][subphase_id]['fields'].append(row)
    #                 elif not report.subphases:
    #                     # Add fields directly to phase if not using subphases
    #                     if field_by_project.field.type == "TASK_WITH_SUBTASKS" and field_by_project.field.subtasks:
    #                         expanded_rows = expand_task_with_subtasks(field_by_project, language)
    #                         organized_data[phase_id]['fields'].extend(expanded_rows)
    #                     elif field_by_project.field.type == "TASK":
    #                         formatted_value = self.format_task_value(field_by_project.value, language)
    #                         row = {
    #                             'is_subtask': False,
    #                             'field_name': field_by_project.field.name,
    #                             'field_type': field_by_project.field.type,
    #                             'status': field_by_project.status,
    #                             'value': formatted_value,
    #                             'observations': getattr(field_by_project, 'observations', ''),
    #                             'original_field': field_by_project.field
    #                         }
    #                         organized_data[phase_id]['fields'].append(row)
    #                     else:
    #                         row = {
    #                             'is_subtask': False,
    #                             'field_name': field_by_project.field.name,
    #                             'field_type': field_by_project.field.type,
    #                             'status': field_by_project.status,
    #                             'value': field_by_project.value,
    #                             'observations': getattr(field_by_project, 'observations', ''),
    #                             'original_field': field_by_project.field
    #                         }
    #                         organized_data[phase_id]['fields'].append(row)
    #     else:
    #         # If phases are not included, just list all fields
    #         all_rows = []
    #         for field_by_project in filtered_fields:
    #             if getattr(field_by_project.field, 'type', None) == "TASK_WITH_SUBTASKS" and getattr(field_by_project.field, 'subtasks', None):
    #                 expanded_rows = expand_task_with_subtasks(field_by_project, language)
    #                 all_rows.extend(expanded_rows)
    #             elif getattr(field_by_project.field, 'type', None) == "TASK":
    #                 formatted_value = self.format_task_value(field_by_project.value, language)
    #                 row = {
    #                     'is_subtask': False,
    #                     'field_name': field_by_project.field.name,
    #                     'field_type': field_by_project.field.type,
    #                     'status': field_by_project.status,
    #                     'value': formatted_value,
    #                     'observations': getattr(field_by_project, 'observations', ''),
    #                     'original_field': field_by_project.field
    #                 }
    #                 all_rows.append(row)
    #             else:
    #                 row = {
    #                     'is_subtask': False,
    #                     'field_name': field_by_project.field.name,
    #                     'field_type': field_by_project.field.type,
    #                     'status': field_by_project.status,
    #                     'value': field_by_project.value,
    #                     'observations': getattr(field_by_project, 'observations', ''),
    #                     'original_field': field_by_project.field
    #                 }
    #                 all_rows.append(row)
    #         organized_data['all_fields'] = {
    #             'fields': all_rows
    #         }

    #     # print("Organized Data:", organized_data)  # Debugging line to check the structure

    #     # Build the complete context
    #     static_dir = os.path.join(settings.BASE_DIR, 'static')
    #     context = {
    #         'project': project,
    #         'report': report,
    #         'fields': filtered_fields,
    #         'organized_data': organized_data,
    #         "timestamp": timezone.now().strftime('%d-%m-%Y'),
    #         "css_path": f"file:///{static_dir}/css/styles_rep.css".replace('\\', '/'),
    #         "logo_path": f"file:///{static_dir}/img/logo.png".replace('\\', '/'),
    #         "favicon_path": f"file:///{static_dir}/img/favicon.png".replace('\\', '/'),
    #     }
    #     return context
    
    # def format_task_value(self, value, language):
    #     """
    #     Formats the task value to uppercase if it's a string.
    #     """
    #     if value.upper() == "NOT_STARTED":
    #         return "NO INICIADO" if language == 'es' else "NOT STARTED"
    #     elif value.upper() == "IN_PROGRESS":
    #         return "EN PROGRESO" if language == 'es' else "IN PROGRESS"
    #     elif value.upper() == "COMPLETED":
    #         return "COMPLETADO" if language == 'es' else "COMPLETED"
    #     elif value.upper() == "CANCELED":
    #         return "CANCELADO" if language == 'es' else "CANCELED"
    #     else:
    #         return value.upper() if isinstance(value, str) else value



class GenerateReportingPDFView(APIView):
    """
    Endpoint para generar un PDF de un reporte.
    Genera un reporte en formato PDF con los detalles del proyecto, 
    incluyendo información sobre las fases, subfases y campos.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.editar_config')]

    @swagger_auto_schema(
        operation_summary="Generar PDF del plan de proyecto",
        manual_parameters=[
            openapi.Parameter(
                'project_id', 
                openapi.IN_PATH, 
                description="ID del proyecto", 
                type=openapi.TYPE_INTEGER, 
                required=True
            ),
            openapi.Parameter(
                'language', 
                openapi.IN_QUERY, 
                description="Idioma del reporte (es o en)", 
                type=openapi.TYPE_STRING, 
                required=True,
                enum=['es', 'en']
            ),
            openapi.Parameter(
                'report_id',
                openapi.IN_QUERY,
                description="ID del reporte",
                type=openapi.TYPE_INTEGER,
                required=True
            ),
        ],
        responses={
            200: openapi.Response(description="PDF generado exitosamente"),
            400: "Datos inválidos o error en la generación",
            404: "Proyecto no encontrado",
            500: "Error interno del servidor"
        }
    )
    def get(self, request, project_id, *args, **kwargs):
        try:
            language = request.GET.get('language', 'es')
            if language not in ['es', 'en']:
                return Response({"error": "Idioma no válido. Use 'es' o 'en'"}, status=status.HTTP_400_BAD_REQUEST)

            try:
                project = Project.objects.get(id=project_id)
            except Project.DoesNotExist:
                return Response({"error": "Proyecto no encontrado"}, status=status.HTTP_404_NOT_FOUND)

            try:
                report = Report.objects.get(id=request.GET.get('report_id'))
            except Report.DoesNotExist:
                return Response({"error": "Reporte no encontrado"}, status=status.HTTP_404_NOT_FOUND)

            report_fields = report.fields.all()
            fields_by_project = FieldsByProject.objects.filter(
                project=project,
                field__in=report_fields,
                active=True
            ).select_related('field', 'phase')

            context = self._build_context(project, report, fields_by_project, language)
            template_name = f'reporting_{language}.html'

            if language == 'en':
                filename = f"{timezone.now().strftime('%y%m%d')}_{project.lid}_{project.company_name}_{report.name_en}_EN.pdf"
            else:
                filename = f"{timezone.now().strftime('%y%m%d')}_{project.lid}_{project.company_name}_{report.name}_ES.pdf"

            context['filename'] = filename
            # context['header_img_path'] = f"file:///{os.path.join(settings.BASE_DIR, 'static', 'img', 'header_generated.png')}".replace('\\', '/')

            html_string = render_to_string(template_name, context)

            # WeasyPrint setup
            css_file_path = os.path.join(settings.BASE_DIR, 'static', 'css', 'styles_rep.css')
            pdf_file = HTML(string=html_string, base_url=request.build_absolute_uri()).write_pdf(stylesheets=[CSS(filename=css_file_path)])

            response = HttpResponse(pdf_file, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            return response

        except Exception as e:
            return Response({"error": f"Error inesperado: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _build_context(self, project, report, fields_by_project, language):
        # Filter fields_by_project based on the report filters
        filtered_fields = fields_by_project
        
        if report.filters:
            # Apply filters based on field type and status
            filtered_fields = []
            for field_by_project in fields_by_project:
                include_field = False
                field_type = getattr(field_by_project.field, 'type', None)
                field_status = field_by_project.status.lower() if field_by_project.status else None
                # Check if this field matches any filter criteria
                if field_type in report.filters:
                    type_filters = report.filters[field_type]
                    allowed_statuses = type_filters.get('statuses', [])
                    if field_status in allowed_statuses:
                        include_field = True
                if include_field:
                    filtered_fields.append(field_by_project)
        

        def expand_task_with_subtasks(field_by_project, language):
            """
            Expands a TASK_WITH_SUBTASKS field into multiple row objects
            """
            rows = []
            
            # Main task row (parent)
            main_row = {
                'is_subtask': False,
                'field_name': field_by_project.field.name_en if language == 'en' and field_by_project.field.name_en else field_by_project.field.name,
                'field_type': field_by_project.field.type,
                'status': field_by_project.status,
                'value': '',  # Empty value for main task
                'observations': getattr(field_by_project, 'observations', ''),
                'original_field': field_by_project.field
            }
            rows.append(main_row)
            
            # Subtask rows
            try:
                import json
                subtasks_data = field_by_project.field.subtasks
                if isinstance(subtasks_data, list):
                    subtasks = subtasks_data
                else:
                    subtasks = json.loads(subtasks_data)
                
                # Get values for subtasks
                value = getattr(field_by_project, 'value', '')
                if isinstance(value, str):
                    try:
                        values = json.loads(value)
                        if not isinstance(values, list):
                            values = [values]
                    except Exception:
                        values = [value] * len(subtasks)
                elif isinstance(value, list):
                    values = value
                else:
                    values = [value] * len(subtasks)
                
                # Create subtask rows
                for i, subtask in enumerate(subtasks):
                    subtask_status = values[i] if i < len(values) else ''

                    match subtask_status:
                        case 'completado':
                            subtask_status = 'COMPLETED'
                        case 'en_progreso':
                            subtask_status = 'IN_PROGRESS'
                        case 'no_empezado':
                            subtask_status = 'NOT_STARTED'

                    subtask_row = {
                        'is_subtask': True,
                        'field_name': subtask.get('title', ''),
                        'field_type': field_by_project.field.type,
                        'status': subtask_status,
                        'value': subtask_status,
                        'observations': '',
                        'original_field': field_by_project.field
                    }
                    rows.append(subtask_row)
                    
            except Exception as e:
                print(f"Error processing subtasks: {e}")
            
            return rows

        # Build organized data structure for template
        organized_data = {}
        
        if report.phases:
            # Group by phases
            for field_by_project in filtered_fields:
                phase = field_by_project.phase
                if phase:
                    phase_id = phase.id
                    if phase_id not in organized_data:
                        organized_data[phase_id] = {
                            'phase': phase,
                            'subphases': {} if report.subphases else None,
                            'fields': [] if not report.subphases else None
                        }
                    
                    if report.subphases and field_by_project.field.subphase:
                        # Group by subphases within phases
                        subphase = field_by_project.field.subphase
                        subphase_id = subphase.id
                        if subphase_id not in organized_data[phase_id]['subphases']:
                            organized_data[phase_id]['subphases'][subphase_id] = {
                                'subphase': subphase,
                                'fields': []
                            }
                        
                        # Process field based on type
                        if field_by_project.field.type == "TASK_WITH_SUBTASKS" and field_by_project.field.subtasks:
                            # Expand into multiple rows
                            expanded_rows = expand_task_with_subtasks(field_by_project, language)
                            organized_data[phase_id]['subphases'][subphase_id]['fields'].extend(expanded_rows)
                        elif field_by_project.field.type == "TASK":
                            # Single row with formatted value
                            formatted_value = self.format_task_value(field_by_project.value, language)
                            row = {
                                'is_subtask': False,
                                'field_name': field_by_project.field.name_en if language == 'en' and field_by_project.field.name_en else field_by_project.field.name,
                                'field_type': field_by_project.field.type,
                                'status': field_by_project.status,
                                'value': formatted_value,
                                'observations': getattr(field_by_project, 'observations', ''),
                                'original_field': field_by_project.field
                            }
                            organized_data[phase_id]['subphases'][subphase_id]['fields'].append(row)
                        else:
                            # Other field types
                            row = {
                                'is_subtask': False,
                                'field_name': field_by_project.field.name_en if language == 'en' and field_by_project.field.name_en else field_by_project.field.name,
                                'field_type': field_by_project.field.type,
                                'status': field_by_project.status,
                                'value': field_by_project.value,
                                'observations': getattr(field_by_project, 'observations', ''),
                                'original_field': field_by_project.field
                            }
                            organized_data[phase_id]['subphases'][subphase_id]['fields'].append(row)
                    elif not report.subphases:
                        # Add fields directly to phase if not using subphases
                        if field_by_project.field.type == "TASK_WITH_SUBTASKS" and field_by_project.field.subtasks:
                            expanded_rows = expand_task_with_subtasks(field_by_project, language)
                            organized_data[phase_id]['fields'].extend(expanded_rows)
                        elif field_by_project.field.type == "TASK":
                            formatted_value = self.format_task_value(field_by_project.value, language)
                            row = {
                                'is_subtask': False,
                                'field_name': field_by_project.field.name_en if language == 'en' and field_by_project.field.name_en else field_by_project.field.name,
                                'field_type': field_by_project.field.type,
                                'status': field_by_project.status,
                                'value': formatted_value,
                                'observations': getattr(field_by_project, 'observations', ''),
                                'original_field': field_by_project.field
                            }
                            organized_data[phase_id]['fields'].append(row)
                        else:
                            row = {
                                'is_subtask': False,
                                'field_name': field_by_project.field.name_en if language == 'en' and field_by_project.field.name_en else field_by_project.field.name,
                                'field_type': field_by_project.field.type,
                                'status': field_by_project.status,
                                'value': field_by_project.value,
                                'observations': getattr(field_by_project, 'observations', ''),
                                'original_field': field_by_project.field
                            }
                            organized_data[phase_id]['fields'].append(row)
        else:
            # If phases are not included, just list all fields
            all_rows = []
            for field_by_project in filtered_fields:
                if getattr(field_by_project.field, 'type', None) == "TASK_WITH_SUBTASKS" and getattr(field_by_project.field, 'subtasks', None):
                    expanded_rows = expand_task_with_subtasks(field_by_project, language)
                    all_rows.extend(expanded_rows)
                elif getattr(field_by_project.field, 'type', None) == "TASK":
                    formatted_value = self.format_task_value(field_by_project.value, language)
                    row = {
                        'is_subtask': False,
                        'field_name': field_by_project.field.name_en if language == 'en' and field_by_project.field.name_en else field_by_project.field.name,
                        'field_type': field_by_project.field.type,
                        'status': field_by_project.status,
                        'value': formatted_value,
                        'observations': getattr(field_by_project, 'observations', ''),
                        'original_field': field_by_project.field
                    }
                    all_rows.append(row)
                else:
                    row = {
                        'is_subtask': False,
                        'field_name': field_by_project.field.name_en if language == 'en' and field_by_project.field.name_en else field_by_project.field.name,
                        'field_type': field_by_project.field.type,
                        'status': field_by_project.status,
                        'value': field_by_project.value,
                        'observations': getattr(field_by_project, 'observations', ''),
                        'original_field': field_by_project.field
                    }
                    all_rows.append(row)
            organized_data['all_fields'] = {
                'fields': all_rows
            }

        # print("Organized Data:", organized_data)  # Debugging line to check the structure

        # Build the complete context
        static_dir = os.path.join(settings.BASE_DIR, 'static')
        context = {
            'project': project,
            'report': report,
            'fields': filtered_fields,
            'organized_data': organized_data,
            "timestamp": timezone.now().strftime('%d-%m-%Y %H:%M:%S'),
            "css_path": f"file:///{static_dir}/css/styles_rep.css".replace('\\', '/'),
            "logo_path": f"file:///{static_dir}/img/logo.png".replace('\\', '/'),
            "triangle_path": f"file:///{static_dir}/img/triangulo_rm.png".replace('\\', '/'),
            "logo_small_path": f"file:///{static_dir}/img/logo-tamano-simbolo_cut.png".replace('\\', '/'),
            "favicon_path": f"file:///{static_dir}/img/favicon.png".replace('\\', '/'),
        }
        return context
    
    def format_task_value(self, value, language):
        """
        Formats the task value to uppercase if it's a string.
        """
        if value is None:
            return ""
        
        if not isinstance(value, str):
            value = str(value)
            
        try:
            if value.upper() == "NOT_STARTED":
                return "NO INICIADO" if language == 'es' else "NOT STARTED"
            elif value.upper() == "IN_PROGRESS":
                return "EN PROGRESO" if language == 'es' else "IN PROGRESS"
            elif value.upper() == "COMPLETED":
                return "COMPLETADO" if language == 'es' else "COMPLETED"
            elif value.upper() == "CANCELED":
                return "CANCELADO" if language == 'es' else "CANCELED"
            else:
                return value.upper() if isinstance(value, str) else value
        except AttributeError:
            # If value doesn't have upper() method, return as string
            return str(value)