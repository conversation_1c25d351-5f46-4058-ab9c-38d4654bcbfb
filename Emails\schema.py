import graphene
from graphene_django.types import DjangoObjectType
from rest_framework.permissions import IsAuthenticated
from graphql_jwt.decorators import permission_required

from RMO.decorators import custom_login_required

from .models import EmailTemplate

class EmailTemplateType(DjangoObjectType):
    class Meta:
        model = EmailTemplate
        fields = ("id", "name", "subject", "body", "topic", "is_external", "created_at")

class Query(graphene.ObjectType):
    all_email_templates = graphene.List(EmailTemplateType)
    email_template = graphene.Field(EmailTemplateType, id=graphene.ID(required=True))

    @custom_login_required
    @permission_required('Users.visualizar_config')
    def resolve_all_email_templates(self, info):
        return EmailTemplate.objects.all()

    @custom_login_required
    @permission_required('Users.visualizar_config')
    def resolve_email_template(self, info, id):
        try:
            return EmailTemplate.objects.get(id=id)
        except EmailTemplate.DoesNotExist:
            return None

class CreateEmailTemplate(graphene.Mutation):
    class Arguments:
        name = graphene.String(required=True)
        subject = graphene.String(required=True)
        body = graphene.String(required=True)
        topic = graphene.String()
        is_external = graphene.Boolean()

    email_template = graphene.Field(EmailTemplateType)

    @custom_login_required
    @permission_required('Users.editar_config')
    def mutate(self, info, name, subject, body, topic=None, is_external=False):
        template = EmailTemplate.objects.create(
            name=name, 
            subject=subject, 
            body=body, 
            topic=topic,
            is_external=is_external
        )
        return CreateEmailTemplate(email_template=template)

class UpdateEmailTemplate(graphene.Mutation):
    class Arguments:
        id = graphene.ID(required=True)
        name = graphene.String()
        subject = graphene.String()
        body = graphene.String()
        topic = graphene.String()
        is_external = graphene.Boolean()

    email_template = graphene.Field(EmailTemplateType)

    @custom_login_required
    @permission_required('Users.editar_config')
    def mutate(self, info, id, name=None, subject=None, body=None, topic=None, is_external=None):
        try:
            template = EmailTemplate.objects.get(id=id)
        except EmailTemplate.DoesNotExist:
            raise Exception("Plantilla de email no encontrada.")
        if name is not None:
            template.name = name
        if subject is not None:
            template.subject = subject
        if body is not None:
            template.body = body
        if topic is not None:
            template.topic = topic
        if is_external is not None:
            template.is_external = is_external
        template.save()
        return UpdateEmailTemplate(email_template=template)

class DeleteEmailTemplate(graphene.Mutation):
    class Arguments:
        id = graphene.ID(required=True)
    ok = graphene.Boolean()

    @custom_login_required
    @permission_required('Users.editar_config')
    def mutate(self, info, id):
        try:
            template = EmailTemplate.objects.get(id=id)
            template.delete()
            return DeleteEmailTemplate(ok=True)
        except EmailTemplate.DoesNotExist:
            return DeleteEmailTemplate(ok=False)

class Mutation(graphene.ObjectType):
    create_email_template = CreateEmailTemplate.Field()
    update_email_template = UpdateEmailTemplate.Field()
    delete_email_template = DeleteEmailTemplate.Field()
