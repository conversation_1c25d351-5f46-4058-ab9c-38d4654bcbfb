from django.db.models.signals import post_migrate
from django.dispatch import receiver
from django.core.management import call_command

@receiver(post_migrate)
def always_import_users_core(sender, **kwargs):
    # Ejecutar solo una vez tras toda la migración, no por app
    if sender.name == "contenttypes":  # contenttypes siempre es migrada, y es una señal común a todas
        print("🟢 Ejecutando import_users_core tras migrate...")
        call_command("import_users_core")
