# serializers.py

from rest_framework import serializers
from .models import Template
from Fields.models import Field
from Rules.models import Rule
from Notifications.models import Notification

class TemplateSerializer(serializers.ModelSerializer):
    # Usamos PrimaryKeyRelatedField para pasar listas de IDs en el POST
    fields = serializers.PrimaryKeyRelatedField(
        many=True, queryset=Field.objects.all()
    )
    rules = serializers.PrimaryKeyRelatedField(
        many=True, queryset=Rule.objects.all()
    )
    notifications = serializers.PrimaryKeyRelatedField(
        many=True, queryset=Notification.objects.all()
    )

    class Meta:
        model = Template
        fields = ['id', 'name', 'description', 'fields', 'rules', 'notifications', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']


class TemplateListSerializer(serializers.ModelSerializer):
    class Meta:
        model = Template
        fields = ['id', 'name', 'description', 'is_active']
