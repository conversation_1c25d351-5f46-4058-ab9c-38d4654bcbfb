from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework.permissions import IsAuthenticated

from Templates.models import Template
from Fields.models import Field
from Rules.models import Rule
from Notifications.models import Notification
from Templates.serializers import TemplateListSerializer
from Users.utilFunctions import permissions_required

class TemplateCreateView(APIView):
    """
    Crea un nuevo Template a partir de un JSON con templateData, selectedFields, selectedRules y selectedNotifications.
    """

    permission_classes = [IsAuthenticated, permissions_required('Users.editar_config')]
    @swagger_auto_schema(
        operation_summary="Crear un nuevo Template",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["templateData", "selectedFields", "selectedRules", "selectedNotifications"],
            properties={
                "templateData": openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    required=["name", "description"],
                    properties={
                        "name": openapi.Schema(type=openapi.TYPE_STRING),
                        "description": openapi.Schema(type=openapi.TYPE_STRING),
                    },
                ),
                "selectedFields": openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Items(type=openapi.TYPE_INTEGER)),
                "selectedRules": openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Items(type=openapi.TYPE_INTEGER)),
                "selectedNotifications": openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Items(type=openapi.TYPE_INTEGER)),
            },
        ),
        responses={
            200: openapi.Response(description="Plantilla creada correctamente."),
            400: openapi.Response(description="Error de validación o datos inválidos."),
        }
    )
    def post(self, request):
        data = request.data
        template_data = data.get('templateData', {})
        name = template_data.get('name')
        description = template_data.get('description')
        field_ids = data.get('selectedFields', [])
        rule_ids = data.get('selectedRules', [])
        notification_ids = data.get('selectedNotifications', [])

        if not name:
            return Response({'error': 'El nombre es obligatorio.'}, status=400)
        if Template.objects.filter(name=name).exists():
            return Response({'error': 'Ya existe un Template con ese nombre.'}, status=400)

        # Validar y obtener instancias
        fields = Field.objects.filter(id__in=field_ids)
        if len(fields) != len(field_ids):
            return Response({'error': 'Algún campo no existe.'}, status=400)
        rules = Rule.objects.filter(id__in=rule_ids)
        if len(rules) != len(rule_ids):
            return Response({'error': 'Alguna regla no existe.'}, status=400)
        notifications = Notification.objects.filter(id__in=notification_ids)
        if len(notifications) != len(notification_ids):
            return Response({'error': 'Alguna notificación no existe.'}, status=400)

        template = Template.objects.create(name=name, description=description)
        template.fields.set(fields)
        template.rules.set(rules)
        template.notifications.set(notifications)
        template.generate_full_definition()
        template.save()

        return Response({
            'success': True,
            'message': 'Plantilla creada correctamente.'
        })


class TemplateListView(APIView):
    """
    Lista todas las plantillas disponibles (id + name)
    """

    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_config')]
    @swagger_auto_schema(
        operation_summary="Listar todas las plantillas",
        responses={200: openapi.Response(description="Listado de plantillas")}
    )
    def get(self, request):
        templates = Template.objects.all()
        serializer = TemplateListSerializer(templates, many=True)
        return Response(serializer.data)


class TemplateListFilteredView(APIView):
    """
    Lista todas las plantillas activas (id + name)
    """

    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]
    @swagger_auto_schema(
        operation_summary="Listar todas las plantillas activas",
        responses={200: openapi.Response(description="Listado de plantillas")}
    )
    def get(self, request):
        templates = Template.objects.filter(is_active=True)  # Filtrar plantillas activas
        serializer = TemplateListSerializer(templates, many=True)
        return Response(serializer.data)


class TemplateDetailView(APIView):
    """
    Devuelve toda la información de una plantilla específica (incluyendo campos, reglas, notificaciones y full_definition).
    """

    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_config')]
    @swagger_auto_schema(
        operation_summary="Obtener detalles completos de una plantilla",
        responses={200: openapi.Response(description="Detalle de la plantilla"), 404: openapi.Response(description="Plantilla no encontrada")}
    )
    def get(self, request, pk):
        try:
            template = Template.objects.get(id=pk)
        except Template.DoesNotExist:
            return Response({'error': 'Plantilla no encontrada.'}, status=404)

        if not template.full_definition:
            template.generate_full_definition()

        data = {
            'id': template.id,
            'name': template.name,
            'description': template.description,
            'full_definition': template.full_definition,
            'created_at': template.created_at,
            'updated_at': template.updated_at
        }
        return Response(data)


class TemplateToggleActiveView(APIView):
    """
    Alterna el estado de is_active de una plantilla (True <-> False)
    """

    permission_classes = [IsAuthenticated, permissions_required('Users.editar_config')]
    @swagger_auto_schema(
        operation_summary="Alternar el estado de una plantilla (activar/desactivar)",
        responses={
            200: openapi.Response(description="Estado de la plantilla alternado correctamente."),
            404: openapi.Response(description="Plantilla no encontrada."),
        }
    )
    def post(self, request, pk):
        try:
            template = Template.objects.get(id=pk)
        except Template.DoesNotExist:
            return Response({'error': 'Plantilla no encontrada.'}, status=404)
        template.is_active = not template.is_active
        template.save()
        return Response({'success': True, 'is_active': template.is_active, 'message': f'Plantilla {"activada" if template.is_active else "deshabilitada"} correctamente.'})


class TemplateUpdateView(APIView):
    """
    Modifica solo las reglas y notificaciones de una plantilla y actualiza el full_definition.
    """

    permission_classes = [IsAuthenticated, permissions_required('Users.editar_config')]
    @swagger_auto_schema(
        operation_summary="Actualizar reglas y notificaciones de una plantilla",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["rules", "notifications"],
            properties={
                "rules": openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Items(type=openapi.TYPE_INTEGER)),
                "notifications": openapi.Schema(type=openapi.TYPE_ARRAY, items=openapi.Items(type=openapi.TYPE_INTEGER)),
            },
        ),
        responses={
            200: openapi.Response(description="Plantilla actualizada correctamente."),
            400: openapi.Response(description="Algún campo necesario para las reglas o notificaciones no está en la plantilla."),
            404: openapi.Response(description="Plantilla no encontrada."),
        }
    )
    def post(self, request, pk):
        try:
            template = Template.objects.get(id=pk)
        except Template.DoesNotExist:
            return Response({'error': 'Plantilla no encontrada.'}, status=404)
        rule_ids = request.data.get('rules', [])
        notification_ids = request.data.get('notifications', [])
        
        rules = Rule.objects.filter(id__in=rule_ids)
        if len(rules) != len(rule_ids):
            return Response({'error': 'Alguna regla no existe.'}, status=400)
        
        notifications = Notification.objects.filter(id__in=notification_ids)
        if len(notifications) != len(notification_ids):
            return Response({'error': 'Alguna notificación no existe.'}, status=400)
        
        # Validar que los campos necesarios están en la plantilla
        template_field_ids = set(template.fields.values_list('id', flat=True))
        for rule in rules:
            if rule.origin_field.id not in template_field_ids or rule.target_field.id not in template_field_ids:
                return Response({'error': f'La regla "{rule.name}" requiere campos que no están en la plantilla.'}, status=400)
        
        for notif in notifications:
            if notif.trigger_field.id not in template_field_ids:
                return Response({'error': f'La notificación "{notif.name}" requiere un campo que no está en la plantilla.'}, status=400)
        
        template.rules.set(rules)
        template.notifications.set(notifications)
        template.generate_full_definition()
        template.save()
        return Response({'success': True, 'message': 'Plantilla actualizada correctamente.'})


