from rest_framework.test import APITestCase
from django.urls import reverse
from Users.models import User
from rest_framework_simplejwt.tokens import RefreshToken

class ProfileUpdateTest(APITestCase):
    def setUp(self):
        # Creamos un usuario normal
        self.user = User.objects.create_user(
            username="normaluser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Original",
            last_name="User"
        )
        # Creamos el token JWT directamente
        self.token = str(RefreshToken.for_user(self.user).access_token)
        # Lo usamos para autenticar las peticiones
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')

    def test_user_cannot_change_email_or_username(self):
        url = reverse('me')  # Asegúrate que en urls.py: path('profile/', ..., name='me')
        data = {
            "first_name": "NuevoNombre",
            "email": "<EMAIL>",
            "username": "otro_nombre"
        }

        response = self.client.put(url, data, format='json')
        self.assertEqual(response.status_code, 200)

        self.user.refresh_from_db()

        # ✅ Sí se puede cambiar el nombre
        self.assertEqual(self.user.first_name, "NuevoNombre")

        # ❌ No se puede cambiar el email ni el username
        self.assertEqual(self.user.email, "<EMAIL>")
        self.assertEqual(self.user.username, "normaluser")


class AdminProfileUpdateTest(APITestCase):
    def setUp(self):
        # Creamos un usuario con permisos de admin (is_staff = True)
        self.admin_user = User.objects.create_user(
            username="adminuser",
            email="<EMAIL>",
            password="adminpass123",
            first_name="Admin",
            last_name="User",
            is_staff=True  # 👈 Importante
        )
        self.token = str(RefreshToken.for_user(self.admin_user).access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.token}')

    def test_admin_can_change_email_and_username(self):
        url = reverse('me')
        data = {
            "first_name": "AdminNuevo",
            "email": "<EMAIL>",
            "username": "nuevoadmin"
        }

        response = self.client.put(url, data, format='json')
        self.assertEqual(response.status_code, 200)

        self.admin_user.refresh_from_db()

        # ✅ Admin puede cambiar todo
        self.assertEqual(self.admin_user.first_name, "AdminNuevo")
        self.assertEqual(self.admin_user.email, "<EMAIL>")
        self.assertEqual(self.admin_user.username, "nuevoadmin")
