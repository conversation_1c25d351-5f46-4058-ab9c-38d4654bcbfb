# Generated by Django 3.2.25 on 2025-05-30 08:36

from decimal import Decimal
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Phase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('order', models.IntegerField()),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='Subphase',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('order', models.IntegerField()),
                ('phase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Fields.phase')),
            ],
            options={
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='Field',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('name_en', models.CharField(blank=True, max_length=255, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('type', models.CharField(choices=[('INFORMATIVE', 'Informative'), ('SELECTION', 'Selection'), ('TASK', 'Task'), ('DOCUMENT', 'Document'), ('TASK_WITH_SUBTASKS', 'Task with Subtasks')], max_length=50)),
                ('status', models.CharField(choices=[('NOT_STARTED', 'Not Started'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed')], default='NOT_STARTED', max_length=50)),
                ('weight', models.DecimalField(choices=[(Decimal('0'), '0'), (Decimal('0.25'), '0.25'), (Decimal('0.5'), '0.5'), (Decimal('0.75'), '0.75'), (Decimal('1'), '1')], decimal_places=2, default=Decimal('0'), max_digits=3)),
                ('is_milestone', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('selection_options', models.JSONField(blank=True, null=True)),
                ('subtasks', models.JSONField(blank=True, null=True)),
                ('subphase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='Fields.subphase')),
            ],
        ),
    ]
