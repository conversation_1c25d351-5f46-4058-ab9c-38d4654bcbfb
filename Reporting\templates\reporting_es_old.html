<!DOCTYPE html>
<html lang="es" xmlns:mso="urn:schemas-microsoft-com:office:office" xmlns:msdt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>{{ filename }}</title>
        {% if css_path %}
        <link rel="stylesheet" href="{{ css_path }}" style="z-index: 99;">
        {% endif %}
    </head>
    <body>
        <div class="container">
            <!-- Portada -->
            <section class="cover-page">
                <div class="cover-header">
                    <h1><strong>{{ project.lid }} -</strong> {{ project.company_name }}</h1>
                    <p class="subtitle">{{ report.name }}</p>
                </div>
                <div class="cover-footer">
                    <img src="{{ logo_path }}" alt="ROSCLAR Logo" class="logo" />
                    <p class="timestamp">{{ timestamp }}</p>
                </div>
            </section>


            <!-- Página del informe -->
            <section class="report-page">
                <!-- Fixed header image for all pages except cover -->
                <img src="{{ header_img_path }}" class="header-img" alt="Header" />

                <header class="report-header">
                    <div class="info-block">
                        <h3>DATOS IMPLEMENTACIÓN</h3>
                        <p><strong>Empresa:</strong> {{ project.company_name }}<br>
                            <strong>Agregador:</strong> {{ project.aggregator }}<br>
                            <strong>Tipo:</strong> {{ project.implementation_type }}<br>
                            <strong>GDL:</strong> {{ project.golive_initial_date|date:"d/m/y" }}
                        </p>
                    </div>
                    <div class="info-block">
                        <h3>EQUIPO ROSCLAR</h3>
                        <p><strong>Implementador 1:</strong> {{ project.implementer1.name }}<br>
                            <strong>Implementador 2:</strong> {{ project.implementer2.name }}<br>
                            <strong>Backup:</strong> {{ project.backup.name }}<br>
                            <strong>Coordinador:</strong> {{ project.coordinator.name }}
                        </p>
                    </div>
                </header>
            
                <main class="report-body">
                    <h2 class="report-title">{{ report.name }}</h2>
                    <p class="report-desc">{{ report.description }}</p>
            
                    {% if report.phases and report.subphases %}
                        {% for phase_id, phase in organized_data.items %}
                            <table class="dynamic-table{% if report.observations %} has-observations{% endif %}">
                                <thead>
                                    <tr class="phase-header">
                                        <th colspan="4">{{ phase.phase.name }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for subphase_id, subphase in phase.subphases.items %}
                                        <tr class="subphase-header">
                                            <th colspan="4">{{ subphase.subphase.name }}</th>
                                        </tr>
                                        {% if forloop.first %}
                                            <tr class="field-header">
                                                <th>Campo</th>
                                                <th>Estado</th>
                                                <th>Valor</th>
                                                {% if report.observations %}<th>Observaciones</th>{% endif %}
                                            </tr>
                                        {% endif %}
                                        {% if subphase.fields %}
                                            {% for field in subphase.fields %}
                                                <tr>
                                                    {% if field.is_subtask %}
                                                        <td style="padding-left: 2em;">&rarr; {{ field.field_name }}</td>
                                                    {% else %}
                                                        <td>{{ field.field_name }}</td>
                                                    {% endif %}
                                                    <td class="status-cell">
                                                        {% if field.status == 'COMPLETED' %}
                                                            <span class="status-dot status-completed"></span>
                                                        {% elif field.status == 'IN_PROGRESS' %}
                                                            <span class="status-dot status-inprogress"></span>
                                                        {% elif field.status == 'NOT_STARTED' %}
                                                            <span class="status-dot status-notstarted"></span>
                                                        {% else %}
                                                            {{ field.status }}
                                                        {% endif %}
                                                    </td>
                                                    <td>{{ field.value }}</td>
                                                    {% if report.observations %}
                                                        <td>{% if not field.is_subtask %}{{ field.observations }}{% endif %}</td>
                                                    {% endif %}
                                                </tr>
                                            {% endfor %}
                                        {% endif %}
                                    {% endfor %}
                                </tbody>
                            </table>
                        {% endfor %}
                    {% elif report.phases %}
                        {% for phase_id, phase in organized_data.items %}
                            <table class="dynamic-table{% if report.observations %} has-observations{% endif %}">
                                <thead>
                                    <tr class="phase-header">
                                        <th colspan="4">{{ phase.phase.name }}</th>
                                    </tr>
                                    <tr class="field-header">
                                        <th>Campo</th>
                                        <th>Estado</th>
                                        <th>Valor</th>
                                        {% if report.observations %}<th>Observaciones</th>{% endif %}
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for field in phase.fields %}
                                        <tr>
                                            {% if field.is_subtask %}
                                                <td style="padding-left: 2em;">&rarr; {{ field.field_name }}</td>
                                            {% else %}
                                                <td><strong>{{ field.field_name }}</strong></td>
                                            {% endif %}
                                            <td class="status-cell">
                                                {% if field.status == 'COMPLETED' %}
                                                    <span class="status-dot status-completed"></span>
                                                {% elif field.status == 'IN_PROGRESS' %}
                                                    <span class="status-dot status-inprogress"></span>
                                                {% elif field.status == 'NOT_STARTED' %}
                                                    <span class="status-dot status-notstarted"></span>
                                                {% else %}
                                                    {{ field.status }}
                                                {% endif %}
                                            </td>
                                            <td>{{ field.value }}</td>
                                            {% if report.observations %}
                                                <td>{% if not field.is_subtask %}{{ field.observations }}{% endif %}</td>
                                            {% endif %}
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        {% endfor %}
                    {% else %}
                        <table class="dynamic-table">
                            <thead>
                                <tr class="field-header">
                                    <th>Campo</th>
                                    <th>Estado</th>
                                    <th>Valor</th>
                                    {% if report.observations %}<th>Observaciones</th>{% endif %}
                                </tr>
                            </thead>
                            <tbody>
                                {% for field in organized_data.all_fields.fields %}
                                    <tr>
                                        {% if field.is_subtask %}
                                            <td style="padding-left: 2em;">&rarr; {{ field.field_name }}</td>
                                        {% else %}
                                            <td><strong>{{ field.field_name }}</strong></td>
                                        {% endif %}
                                        <td class="status-cell">
                                            {% if field.status == 'COMPLETED' %}
                                                <span class="status-dot status-completed"></span>
                                            {% elif field.status == 'IN_PROGRESS' %}
                                                <span class="status-dot status-inprogress"></span>
                                            {% elif field.status == 'NOT_STARTED' %}
                                                <span class="status-dot status-notstarted"></span>
                                            {% else %}
                                                {{ field.status }}
                                            {% endif %}
                                        </td>
                                        <td>{{ field.value }}</td>
                                        {% if report.observations %}
                                            <td>{% if not field.is_subtask %}{{ field.observations }}{% endif %}</td>
                                        {% endif %}
                                    </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    {% endif %}
                </main>
            
                <footer class="report-footer">
                    <p>CL/ MUNTANER 239, Ático (08021) BARCELONA – TEL. 932 173 444 – FAX. 934 151 303<br>
                        <a href="https://www.rosclar.com">www.rosclar.com</a>
                    </p>
                    <img src="{{ logo_path }}" alt="ROSCLAR Logo" class="logo" />
                </div>
                </footer>
            </section>
    </body>
</html>