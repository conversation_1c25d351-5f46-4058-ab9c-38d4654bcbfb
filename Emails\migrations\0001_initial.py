# Generated by Django 3.2.25 on 2025-05-30 10:48

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='EmailTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('subject', models.<PERSON>r<PERSON>ield(max_length=255)),
                ('body', models.TextField()),
                ('email', models.JSONField()),
            ],
        ),
    ]
