{"Query allRules": {"description": "Retrieve all rules", "query": "query { allRules { id name description rule originField { id name } targetField { id name } value status condition action } }"}, "Query Rule": {"description": "Retrieve a specific rule", "query": "query { rule(id: 1) { id name description rule originField { id name } targetField { id name } value status condition action } }"}, "Mutation createRule": {"description": "Create a new rule", "query": "mutation MyMutation { createRule(input: { name: 'New Rule', description: 'Description of the new rule', rule: 'exampleRule', originFieldId: 1, targetFieldId: 2, value: 'exampleValue', status: 'active', condition: 'exampleCondition', action: 'exampleAction' }) { rule { id name } } }"}, "Mutation updateRule": {"description": "Update an existing rule", "query": "mutation { updateRule(id: 1, input: { name: 'Updated Rule', description: 'Updated description' }) { rule { id name description } } }"}, "Mutation deleteRule": {"description": "Delete a rule", "query": "mutation { deleteRule(id: 1) { success } }"}}