# Generated by Django 3.2.25 on 2025-06-10 08:55

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Projects', '0003_alter_project_template'),
    ]

    operations = [
        migrations.CreateModel(
            name='Contact',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('type', models.<PERSON>r<PERSON>ield(max_length=255)),
                ('position', models.CharField(max_length=255, verbose_name='Cargo en la empresa')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='contacts', to='Projects.project')),
            ],
        ),
    ]
