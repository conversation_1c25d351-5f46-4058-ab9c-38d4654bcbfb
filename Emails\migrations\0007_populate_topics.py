# Generated manually

from django.db import migrations

def populate_topics(apps, schema_editor):
    Topic = apps.get_model('Emails', 'Topic')
    
    topics = [
        'Transitions',
        'Tablas De Conceptos',
        'Add-on',
        'Go Live',
        'Payment Method',
        'It Services',
        'Trebla Services',
        'Presentación De Impuestos',
        'Externos'
    ]
    
    # Crear los topics si no existen
    for topic_name in topics:
        Topic.objects.get_or_create(name=topic_name)

def reverse_populate_topics(apps, schema_editor):
    Topic = apps.get_model('Emails', 'Topic')
    Topic.objects.all().delete()

class Migration(migrations.Migration):

    dependencies = [
        ('Emails', '0006_topic'),
    ]

    operations = [
        migrations.RunPython(populate_topics, reverse_populate_topics),
    ]