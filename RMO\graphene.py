import graphene
import graphql_jwt

from Projects.schema import Query as ProjectsQuery, Mutation as ProjectsMutation
from Fields.schema import Query as FieldsQuery, Mutation as FieldsMutation
from Rules.schema import Query as RulesQuery, Mutation as RulesMutation
from Notifications.schema import Query as NotificationsQuery, Mutation as NotificationsMutation
from Emails.schema import Query as EmailsQuery, Mutation as EmailsMutation

class Query(ProjectsQuery, FieldsQuery, RulesQuery, NotificationsQuery, EmailsQuery, graphene.ObjectType):
    pass

class Mutation(ProjectsMutation, FieldsMutation, RulesMutation, NotificationsMutation, EmailsMutation, graphene.ObjectType):
    token_auth = graphql_jwt.ObtainJSONWebToken.Field()
    verify_token = graphql_jwt.Verify.Field()
    refresh_token = graphql_jwt.Refresh.Field()

schema = graphene.Schema(query=Query, mutation=Mutation)