from django.db import models

from Fields.models import Field
from Users.models import RMOUser

class Rule(models.Model):
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True, null=True)
    rule = models.TextField(null=True, blank=True)
    origin_field = models.ForeignKey(Field, on_delete=models.CASCADE, related_name='origin_field_rules', null=False, blank=False)
    target_field = models.ForeignKey(Field, on_delete=models.CASCADE, related_name='target_field_rules', null=False, blank=False)
    value = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(
        max_length=50,
        choices=[
            ('ACTIVE', 'Active'),
            ('INACTIVE', 'Inactive'),
            
        ],
        default='INACTIVE'
    )
    condition = models.CharField(
        max_length=50,
        choices=[
            ('HAS_CONTENT', 'Tiene contenido'),
            ('CONTENT_EQUAL_TO', 'Contenido igual a'),
            ('CONTENT_UNEQUAL_TO', 'Contenido diferente de'),
            ('IS_COMPLETED', 'Está completado'),
            ('IS_NOT_COMPLETED', 'No está completado'),
            ('IN_PROGRESS', 'Está en progreso')
        ]
    )
    action = models.CharField(
        max_length=50,
        choices=[
            ('ENABLE', 'Habilitar'),
            ('DISABLE', 'Deshabilitar'),

        ]
    )

    def __str__(self):
        return self.name

    def format_rule_info(self):
        """
        Returns a formatted string with key info from the rule and related fields.
        """
        if self.condition == 'content_equal_to' or self.condition == 'content_unequal_to':
            rule = (
                f"SI {self.origin_field.name} {self.get_condition_display()} {self.value} ENTONCES {self.get_action_display()} {self.target_field.name}\n"
                
            )
        else:
            rule = (
                f"SI {self.origin_field.name} {self.get_condition_display()} ENTONCES {self.get_action_display()} {self.target_field.name}\n"
            )
        return rule


