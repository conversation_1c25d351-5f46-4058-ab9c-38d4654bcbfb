import graphene
from graphene import Enum
from graphene_django.types import DjangoObjectType
from graphql_jwt.decorators import permission_required

from RMO.decorators import custom_login_required
from .models import Phase, Subphase, Field
from SingleProject.models import FieldsByProject

# ============================== CLASSES DECLARATION ============================== #

class PhaseType(DjangoObjectType):
    class Meta:
        model = Phase
        fields = "__all__"


class SubphaseType(DjangoObjectType):
    class Meta:
        model = Subphase
        fields = "__all__"


class FieldType(DjangoObjectType):
    class Meta:
        model = Field
        fields = '__all__'

    type = graphene.String()
    fields_count = graphene.Int()

    def resolve_type(self, info):
        return self.type  # Ja és una string com 'informative'

    def resolve_fields_count(self, info):
        return FieldsByProject.objects.filter(field=self).count()


class FieldInput(graphene.InputObjectType):
    name = graphene.String()
    name_en = graphene.String()
    description = graphene.String()
    type = graphene.String()
    subphase_id = graphene.ID()
    weight = graphene.Decimal()
    is_milestone = graphene.Boolean()
    selection_options = graphene.JSONString()
    subtasks = graphene.JSONString()


class SubphaseInput(graphene.InputObjectType):
    name = graphene.String()
    phase_id = graphene.ID()


# ============================== QUERIES DECLARATIONS ============================== #

class Query(graphene.ObjectType):
    all_phases = graphene.List(PhaseType)
    all_subphases = graphene.List(SubphaseType)
    all_fields = graphene.List(FieldType)

    phase = graphene.Field(PhaseType, id=graphene.Int(required=True))
    subphase = graphene.Field(SubphaseType, id=graphene.Int(required=True))
    field = graphene.Field(FieldType, id=graphene.Int(required=True))

    @custom_login_required
    @permission_required('Users.visualizar_config')
    def resolve_all_phases(root, info):
        return Phase.objects.all()

    @custom_login_required
    @permission_required('Users.visualizar_config')
    def resolve_all_subphases(root, info):
        return Subphase.objects.all()

    @custom_login_required
    @permission_required('Users.visualizar_config')
    def resolve_all_fields(root, info):
        return Field.objects.all()

    @custom_login_required
    @permission_required('Users.visualizar_config')
    def resolve_phase(root, info, id):
        return Phase.objects.get(pk=id)

    @custom_login_required
    @permission_required('Users.visualizar_config')
    def resolve_subphase(root, info, id):
        return Subphase.objects.get(pk=id)

    @custom_login_required
    @permission_required('Users.visualizar_config')
    def resolve_field(root, info, id):
        return Field.objects.get(pk=id)
    

# ============================== MUTATIONS DECLARATIONS ============================== #

class CreateFieldMutation(graphene.Mutation):
    class Arguments:
        input = FieldInput(required=True)

    field = graphene.Field(FieldType)

    @custom_login_required
    @permission_required('Users.editar_config')
    def mutate(self, info, input):
        subphase = Subphase.objects.get(id=input.subphase_id)

        field = Field.objects.create(
            name=input.name,
            name_en=input.name_en,
            description=input.description,
            type=input.type.upper(),
            subphase=subphase,
            weight=input.weight if input.weight is not None else Field._meta.get_field('weight').default,
            is_milestone=input.is_milestone if input.is_milestone is not None else False,
            selection_options=input.selection_options if input.selection_options is not None else None,
            subtasks=input.subtasks if input.subtasks is not None else None
        )

        return CreateFieldMutation(field=field)
    

class UpdateFieldMutation(graphene.Mutation):
    class Arguments:
        id = graphene.Int(required=True)
        input = FieldInput(required=False)

    field = graphene.Field(FieldType)

    @custom_login_required
    @permission_required('Users.editar_config')
    def mutate(self, info, id, input=None):
        field_obj = Field.objects.get(id=id)
        if input:
            if input.name is not None:
                field_obj.name = input.name
            if input.name_en is not None:
                field_obj.name_en = input.name_en
            if input.description is not None:
                field_obj.description = input.description
            field_obj.save()
        return UpdateFieldMutation(field=field_obj)
    

class DeleteFieldMutation(graphene.Mutation):
    class Arguments:
        id = graphene.Int(required=True)

    success = graphene.Boolean()

    @custom_login_required
    @permission_required('Users.editar_config')
    def mutate(self, info, id):
        field = Field.objects.get(id=id)
        field.delete()
        return DeleteFieldMutation(success=True)


class CreateSubphaseMutation(graphene.Mutation):
    class Arguments:
        input = SubphaseInput(required=True)

    subphase = graphene.Field(SubphaseType)

    @custom_login_required
    @permission_required('Users.editar_config')
    def mutate(self, info, input):
        phase = Phase.objects.get(id=input.phase_id)

        # Check for duplicate subphase name within the same phase
        if Subphase.objects.filter(name=input.name, phase=phase).exists():
            raise Exception(f"A subphase with the name '{input.name}' already exists in this phase.")

        subphase = Subphase.objects.create(
            name=input.name,
            phase=phase,
        )
        return CreateSubphaseMutation(subphase=subphase)


class UpdateSubphaseMutation(graphene.Mutation):
    class Arguments:
        id = graphene.Int(required=True)
        input = SubphaseInput(required=False)

    subphase = graphene.Field(SubphaseType)

    @custom_login_required
    @permission_required('Users.editar_config')
    def mutate(self, info, id, input=None):
        subphase = Subphase.objects.get(id=id)
        if input:
            if input.name is not None:
                subphase.name = input.name
            if input.phase_id is not None:
                subphase.phase = Phase.objects.get(id=input.phase_id)
            subphase.save()
        return UpdateSubphaseMutation(subphase=subphase)


class DeleteSubphaseMutation(graphene.Mutation):
    class Arguments:
        id = graphene.Int(required=True)

    success = graphene.Boolean()

    @custom_login_required
    @permission_required('Users.editar_config')
    def mutate(self, info, id):
        subphase = Subphase.objects.get(id=id)
        subphase.delete()
        return DeleteSubphaseMutation(success=True)


class Mutation(graphene.ObjectType):
    create_field = CreateFieldMutation.Field()
    update_field = UpdateFieldMutation.Field()
    delete_field = DeleteFieldMutation.Field()
    create_subphase = CreateSubphaseMutation.Field()
    update_subphase = UpdateSubphaseMutation.Field()
    delete_subphase = DeleteSubphaseMutation.Field()
