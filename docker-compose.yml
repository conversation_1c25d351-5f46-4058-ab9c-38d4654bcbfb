services:
  api:
    build: .
    command: sh -c "python manage.py migrate && python manage.py runserver 0.0.0.0:8000"
    volumes:
      - .:/code
    ports:
      - "8000:8000"
    env_file:
      - .env
    environment:
      PYTHONUNBUFFERED: 1
    extra_hosts:
      - "rmo.rosclar.com:*************"
    # Add healthcheck
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3