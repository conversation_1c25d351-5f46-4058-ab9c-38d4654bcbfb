from django.contrib.auth.models import Permission
from django.contrib.contenttypes.models import ContentType
from rest_framework.permissions import BasePermission

from .models import RMOUser

def get_custom_permissions():
    """
    Retrieve custom permissions for the RMOUser model.
    Excludes default permissions: add, change, delete, and view.
    """
    content_type = ContentType.objects.get_for_model(RMOUser)
    codenames_not = ["add_rmouser","change_rmouser","delete_rmouser","view_rmouser"]
    
    # Retrieve the custom permissions
    custom_permissions = Permission.objects.filter(content_type=content_type).exclude(codename__in = codenames_not)

    return custom_permissions

def permissions_required(*perms):
    """
    Returns a DRF permission class that requires all given permissions.
    """
    class _Permission(BasePermission):
        def has_permission(self, request, view):
            return all(request.user.has_perm(perm) for perm in perms)
    _Permission.__name__ = f"PermissionsRequired_{'_'.join(perms)}"
    return _Permission