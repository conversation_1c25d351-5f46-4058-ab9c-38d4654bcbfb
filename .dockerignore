# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
db.sqlite3
media/
static/
staticfiles/

# Local development settings
.env.local
.env.development.local
.env.test.local
.env.production.local

# Docker
.dockerignore
Dockerfile
docker-compose*.yml

# Version control
.git
.gitignore
.github/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Node (if using npm/yarn for frontend)
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log

# Virtual environment
venv/
virtualenv/
ENV/

# Testing
.coverage
htmlcov/
.pytest_cache/

# Documentation
docs/
*.md