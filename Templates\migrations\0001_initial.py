# Generated by Django 3.2.25 on 2025-06-02 09:34

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Rules', '0001_initial'),
        ('Notifications', '0002_auto_20250530_1248'),
        ('Fields', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Template',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('fields', models.ManyToManyField(related_name='templates', to='Fields.Field')),
                ('notifications', models.ManyToManyField(related_name='templates', to='Notifications.Notification')),
                ('rules', models.ManyToManyField(related_name='templates', to='Rules.Rule')),
            ],
        ),
    ]
