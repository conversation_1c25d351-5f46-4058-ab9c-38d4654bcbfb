# Generated by Django 3.2.25 on 2025-05-30 08:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Team', '__first__'),
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='RMOUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('name', models.CharField(max_length=255)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('password', models.CharField(default='', max_length=255)),
                ('position', models.CharField(max_length=255)),
                ('team_code', models.CharField(blank=True, max_length=255, null=True)),
                ('first_name', models.Char<PERSON>ield(blank=True, max_length=150, null=True)),
                ('last_name', models.CharField(blank=True, max_length=150, null=True)),
                ('is_staff', models.BooleanField(default=False)),
                ('is_superuser', models.BooleanField(default=False)),
                ('is_active', models.BooleanField(default=True)),
                ('must_change_password', models.BooleanField(default=True)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.Group', verbose_name='groups')),
                ('team', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='Team.team')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.Permission', verbose_name='user permissions')),
            ],
            options={
                'permissions': (('visualizar_proyectos', 'Puede visualizar proyectos'), ('editar_proyectos', 'Puede editar proyectos'), ('visualizar_config', 'Puede visualizar la configuración'), ('editar_config', 'Puede editar la configuración'), ('visualizar_usuarios', 'Puede visualizar usuarios'), ('editar_usuarios', 'Puede editar usuarios'), ('verificar_fase', 'Puede verificar fases'), ('subtareas_completadas', 'Puede marcar subtareas como completadas')),
            },
        ),
    ]
