from django.conf import settings
import pymysql

def create_query_rosclar(query, params=None, db_name="default"):
    """
    Ejecuta una consulta SQL sobre una base de datos definida en settings.DATABASES.

    Args:
        query (str): La consulta SQL a ejecutar.
        params (tuple, optional): Parámetros para evitar SQL injection. Defaults to None.
        db_name (str): Nombre de la base de datos en settings.DATABASES. Defaults to 'default'.

    Returns:
        list: Resultados de la consulta.
    """
    db_conf = settings.DATABASES.get(db_name)
    if not db_conf:
        raise ValueError(f"Database '{db_name}' not found in settings.")
    
    # print("DB CONFIGURACIÓN:")
    # for key in ["HOST", "PORT", "USER", "PASSWORD", "NAME"]:
    #     print(f"  {key} -> {repr(db_conf.get(key))}")


    connection = pymysql.connect(
        host=db_conf.get("HOST", "localhost"),
        user=db_conf["USER"],
        password=db_conf["PASSWORD"],
        database=db_conf["NAME"],
        port=int(db_conf.get("PORT") or 3306),
        charset="utf8mb4",
        cursorclass=pymysql.cursors.Cursor
    )

    with connection.cursor() as cursor:
        cursor.execute(query, params) if params else cursor.execute(query)
        result = cursor.fetchall()

    connection.close()
    return result
