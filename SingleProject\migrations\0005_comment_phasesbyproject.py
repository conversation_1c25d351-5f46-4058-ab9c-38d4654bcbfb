# Generated by Django 3.2.25 on 2025-06-13 10:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('Fields', '0002_remove_field_status'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Projects', '0006_datefield_phase'),
        ('SingleProject', '0004_auto_20250611_1014'),
    ]

    operations = [
        migrations.CreateModel(
            name='Comment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('text', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='comments', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='PhasesByProject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('verified', models.BooleanField(default=False)),
                ('completed', models.BooleanField(default=False)),
                ('comments', models.ManyToManyField(blank=True, related_name='phases_by_comments', to='SingleProject.Comment')),
                ('phase', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='phases_by_phase', to='Fields.phase')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='phases_by_project', to='Projects.project')),
            ],
            options={
                'unique_together': {('project', 'phase')},
            },
        ),
    ]
