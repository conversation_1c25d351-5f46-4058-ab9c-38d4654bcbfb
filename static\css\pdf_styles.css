/* PDF-specific styles that don't rely on external resources */
body {
  font-family: Arial, sans-serif;
  color: #333;
  background: #f9f9f9;
  margin: 0;
  padding: 0;
  width: 1064px;
  height: 778px;
}
  
.container {
  width: 1000px;
  height: 730px;
  margin: auto;
  background: white;
  padding: 2rem;
  padding-top: 1rem;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
}
  
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0rem;
    padding-left: 3rem;
    padding-right: 3rem;
}
  
.logo {
    width: 150px;
}
  
.title {
    color: #00b0d6;
    font-size: 1.6rem;
    font-weight: bolder;
}
  
.slogan {
    font-weight: 700;
    font-size: 0.8rem;
}
  
.implementacion {
    display: flex;
    justify-content: space-between;
    margin-left: 3rem;
    padding-right: 3rem;
}
  
.implementacion-section {
    width: 50%;
    text-align: left;
}
  
.title-section {
    font-size: 1.2rem;
    font-weight: 600;
    color: black;
    margin-bottom: 0.5rem;
    text-decoration: underline;
    text-decoration-color: #fff;
    text-decoration-thickness: 2px;
    text-underline-offset: 6px;
    display: inline-block;
    padding-right: 1.5rem; /* Makes the underline extend to the right */
    position: relative;
}
  
.title-section::after {
    content: "";
    position: absolute;
    bottom: -6px; /* Match the text-underline-offset */
    left: 0;
    width: 120%; /* Extends beyond the text */
    height: 2px;
    background-color: #00b0d6;
}
  
.datos-implementacion-table {
    width: 60%;
    border-collapse: collapse;
    margin-left: 0.4rem;
}
  
.datos-implementacion-item {
    line-height: 1;
}
  
.datos-implementacion-item-title {
    font-weight: 600;
    color: black;
    margin: 0;
    font-size: 0.8rem;
}
  
.w-extra {
    min-width: 120px;
}
  
.datos-implementacion-item-value {
    font-size: 0.8rem;
    margin: 0;
    color: black;
}
  
.avance {
    margin-left: 3rem;
    padding-right: 3rem;
}
  
.avance-table {
    width: 80%;
    border-spacing: 1rem 0.25rem; /* horizontal and vertical spacing */
    margin-left: 5rem;;
    border-collapse: separate;
}
  
.avance-table-header {
    background-color: #e6e6e6;
    color: black;
    font-weight: bold;
    /* padding: 1rem;
    margin: 1rem; */
    border: 4px solid #ccc;
}
  
.avance-table td, .avance-table th {
    padding: 0.3rem;
}
  
.start-cel {
    border: 1px solid #173700;
    background-color: #e2efd9;
    position: relative;
}
  
.migration-cel {
    border: 1px solid #552579;
    background-color: #e1e1ff;
    position: relative;
}
  
.avance-table-row {
    margin-bottom: 0.5rem;
}
  
.start-row {
    background-color: #c6e0b4;
    border: 2px solid #385623;
    font-weight: 600;
}
  
.start-title-col {
    border: 2px solid #385623;
    background-color: #173700;
    color: white;
}
  
.migration-title-col {
    border: 2px solid #7d00dc;
    background-color: #552579;
    color: white;
}
  
/* Collection row styling - Brown theme */
.collection-cel {
    border: 1px solid #734c00;
    background-color: #f3e7d3;
    position: relative;
}
  
.collection-row {
    background-color: #dfc8a4;
    border: 2px solid #8c6024;
    font-weight: 600;
}
  
.collection-title-col {
    border: 2px solid #8c6024;
    background-color: #734c00;
    color: white;
}
  
/* Test row styling - Orange theme */
.test-cel {
    border: 1px solid #b45f00;
    background-color: #ffecd9;
    position: relative;
}
  
.test-row {
    background-color: #ffd3a1;
    border: 2px solid #d47300;
    font-weight: 600;
}
  
.test-title-col {
    border: 2px solid #d47300;
    background-color: #b45f00;
    color: white;
}
  
/* Take off row styling - Light blue theme */
.takeoff-cel {
    border: 1px solid #0070a8;
    background-color: #d9f0ff;
    position: relative;
}
  
.takeoff-row {
    background-color: #a8dcf7;
    border: 2px solid #0091d9;
    font-weight: 600;
}
  
.takeoff-title-col {
    border: 2px solid #0091d9;
    background-color: #0070a8;
    color: white;
}
  
.golive-cel {
    border: 1px solid #003366;
    background-color: #d1e0f0;
    position: relative;
}
  
.golive-row {
    background-color: #a3c2e6;
    border: 2px solid #0a4d99;
    font-weight: 600;
}
  
.golive-title-col {
    border: 2px solid #0a4d99;
    background-color: #003366;
    color: white;
}

/* Date alignment styles */
.date-left {
    float: left;
}

.date-right {
    float: right;
}

.date-separator {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.percent-left {
    float: left;
}

.percent-right {
    float: right;
}

.percent-separator {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}
  
.note {
    font-size: 0.8rem;
    font-style: italic;
    color: black;
    margin-top: 0rem;
    margin-right: 7.5rem;
    margin-bottom: 0rem;
    text-align: right;
}
  
.proceso {
    margin-left: 3rem;
    padding-right: 3rem;
}
  
.grid-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}
  
.grid-item {
    background-color: white;
    border: 1px solid #385622;
    overflow: hidden;
    display: flex;
    height: 50px;
}
  
.grid-item h3 {
    width: 33%;
    margin: 0;
    /* padding: 1rem; */
    background-color: #385622;
    color: white;
    display: flex;
    align-items: center;
    padding-left: 0.2rem;
    justify-content: start;
    font-size: 0.8rem;
    text-align: center;
}
  
.grid-item p {
    width: 66%;
    margin: 0rem;
    padding: 0rem;
    padding-top: 0.3rem;
    padding-left: 0.3rem;
    font-size: 0.5rem;
    line-height: 1.4;
}
  
.grid-start {
    border: 1px solid #385622;
}
  
.grid-start h3 {
    background-color: #385622;
}
  
.grid-migration {
    border: 1px solid #7d00dc;
}
  
.grid-migration h3 {
    background-color: #7d00dc;
}
  
.grid-collection {
    border: 1px solid #8c6024;
}
  
.grid-collection h3 {
    background-color: #8c6024;
}
  
.grid-test {
    border: 1px solid #d47300;
}
  
.grid-test h3 {
    background-color: #d47300;
}
  
.grid-takeoff {
    border: 1px solid #0091d9;
}
  
.grid-takeoff h3 {
    background-color: #0091d9;
}
  
.grid-golive {
    border: 1px solid #0a4d99;
}
  
.grid-golive h3 {
    background-color: #0a4d99;
}
  
.footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding-left: 3rem;
    padding-right: 3rem;
}
  
.footer-content {
    font-size: 0.5rem;
    color: #7c7c7c;
    padding-left: 1rem;  
}
  
.boldy {
    font-weight: bold;
    color: rgb(54, 54, 54);
}
  
.footer-logo {
    width: 40px;
}
.footer-logo img {
    width: 100%;
    height: auto;
}
