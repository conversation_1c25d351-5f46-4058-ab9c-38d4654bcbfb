from django.db import models

from Users.models import RMOUser
from Fields.models import Field
from Emails.models import EmailTemplate

class Notification(models.Model):
    name = models.CharField(max_length=255, unique=True)
    name_en = models.CharField(max_length=255, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    description_en = models.TextField(blank=True, null=True)
    email_template = models.ForeignKey(EmailTemplate, on_delete=models.CASCADE, related_name='notifications')
    trigger_field = models.ForeignKey(Field, on_delete=models.CASCADE, related_name='trigger_notifications')
    trigger_condition = models.Char<PERSON>ield(
        max_length=50,
        choices=[
            ('IS_FILLED', 'Es rellenado'),
            ('ITS_VALUE_IS', 'Su valor es'),
            ('CONTAINS', 'Contiene'),
            ('CHANGES', 'Cambia')
        ]
    )
    value = models.CharField(max_length=255, blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    user_recipients = models.ManyToManyField(RMOUser, related_name='user_notifications')
    team_recipients = models.ManyToManyField(RMOUser, related_name='team_notifications')
    leader_recipients = models.ManyToManyField(RMOUser, related_name='leader_notifications')
    role_recipients = models.ManyToManyField('Projects.TypeOfUsers', related_name='role_notifications')
    all_recipients = models.ManyToManyField(RMOUser, related_name='all_notifications')

    def __str__(self):
        return self.name
    

class NotificationLog(models.Model):
    notification = models.ForeignKey(Notification, on_delete=models.CASCADE, related_name='logs')
    project = models.ForeignKey('Projects.Project', on_delete=models.CASCADE, related_name='notification_logs', null=True, blank=True)

    timestamp = models.DateTimeField(auto_now_add=True)

    subject = models.CharField(max_length=255, blank=True, null=True)
    email = models.TextField(blank=True, null=True)
    recipients = models.TextField(blank=True, null=True, help_text="Lista de direcciones de correo de los destinatarios")

    def __str__(self):
        return f"Log for {self.notification.name} at {self.timestamp}"