import json
from django.core.mail import send_mail

from .models import FieldsByProject, UsersByProject
from Fields.models import Field
from Fields.models import Phase, Subphase
from Rules.models import Rule
from Projects.models import Project, TypeOfUsers
from SingleProject.models import PhasesByProject
from Notifications.mailFunctions import send_notification

def compute_phase_percentage(project, phase):
    """
    Calcula el porcentaje de avance de una fase en un proyecto dado usando el peso de los campos.
    """
    fields = FieldsByProject.objects.filter(project=project, phase=phase, active=True)
    total_weight = sum(float(f.field.weight) for f in fields)
    completed_weight = sum(float(f.field.weight) for f in fields if f.status == FieldsByProject.COMPLETED or f.status == FieldsByProject.CANCELLED)

    if total_weight == 0:
        return 0
    return int((completed_weight / total_weight) * 100)


def compute_subphase_percentage(project, phase, subphase):
    """
    Calcula el porcentaje de avance de una subfase específica en un proyecto dado usando el peso de los campos.
    """
    fields = FieldsByProject.objects.filter(
        project=project, 
        phase=phase,
        field__subphase=subphase,
        active=True
    )
    total_weight = sum(float(f.field.weight) for f in fields)
    completed_weight = sum(float(f.field.weight) for f in fields if f.status == FieldsByProject.COMPLETED or f.status == FieldsByProject.CANCELLED)

    if total_weight == 0:
        return 0
    return int((completed_weight / total_weight) * 100)


def update_field_status(field):
    """
    Actualiza el estado de un campo en un proyecto dado su ID.
    """
    new_status = FieldsByProject.NOT_STARTED  # default

    match field.field.type:

        case Field.INFORMATIVE:
            if field.value:
                new_status = FieldsByProject.COMPLETED
            else:
                new_status = FieldsByProject.IN_PROGRESS

        case Field.SELECTION:
            options = field.field.selection_options
            if any(option["text"] == field.value and option.get("countsAsCompleted") for option in options):
                new_status = FieldsByProject.COMPLETED
            elif field.value:
                new_status = FieldsByProject.IN_PROGRESS

        case Field.TASK:
            val = str(field.value).strip().lower()
            if val in {"completado", "n/a"}:
                new_status = FieldsByProject.COMPLETED
            elif val in {"en_progreso", "pendiente"}:
                new_status = FieldsByProject.IN_PROGRESS

        case Field.DOCUMENT:
            val = str(field.value).strip().lower()
            if val in {"validado", "inexistente", "n/a"}:
                new_status = FieldsByProject.COMPLETED
            elif val in {"solicitado", "recibido"}:
                new_status = FieldsByProject.IN_PROGRESS

        case Field.TASK_WITH_SUBTASKS:
            try:
                subtasks = field.value if isinstance(field.value, list) else json.loads(field.value)
            except Exception:
                subtasks = []

            if subtasks:
                normalized = [str(sub).strip().lower() for sub in subtasks]
                if all(s in {"completado", "n/a"} for s in normalized):
                    new_status = FieldsByProject.COMPLETED
                elif any(s for s in normalized):
                    new_status = FieldsByProject.IN_PROGRESS

    # Save only if status changed
    if field.status != new_status:
        field.status = new_status
        field.save()


def enforce_rules(list_of_fields, project):
    """
    Aplica las reglas si corresponde a los campos modificados de un proyecto.
    """    
    # Obtener los IDs de los campos en la lista de entrada
    input_field_ids = [field.field.id for field in list_of_fields]
    template = project.template

    # Filtrar solo las reglas de la plantilla cuyo origin_field está en los campos de entrada
    rules = template.rules.filter(origin_field__id__in=input_field_ids)

    for rule in rules:
        origin_field = rule.origin_field
        target_field = rule.target_field

        if origin_field.id in input_field_ids:
            # Obtener el valor del campo de origen
            field_value = next((field.value for field in list_of_fields if field.field.id == origin_field.id), None)

            # Evaluar la condición de la regla
            condition_met = False
            if rule.condition == 'HAS_CONTENT':
                condition_met = bool(field_value)
            elif rule.condition == 'CONTENT_EQUAL_TO':
                condition_met = field_value == rule.value
            elif rule.condition == 'CONTENT_UNEQUAL_TO':
                condition_met = field_value != rule.value
            elif rule.condition == 'IS_COMPLETED':
                # Check field.status instead of value
                field_status = next((field.status for field in list_of_fields if field.field.id == origin_field.id), None)
                condition_met = field_status == FieldsByProject.COMPLETED
            elif rule.condition == 'IS_NOT_COMPLETED':
                field_status = next((field.status for field in list_of_fields if field.field.id == origin_field.id), None)
                condition_met = field_status != FieldsByProject.NOT_STARTED
            elif rule.condition == 'IN_PROGRESS':
                field_status = next((field.status for field in list_of_fields if field.field.id == origin_field.id), None)
                condition_met = field_status == FieldsByProject.IN_PROGRESS

            if condition_met:

                target_field_in_project = FieldsByProject.objects.filter(project=project, field=target_field)

                for field in target_field_in_project:
                    if rule.action == 'ENABLE':
                        field.active = True

                        # # Marcar a fase no completada si se activa un campo de una fase anterior ya completada
                        # phase_in_project = PhasesByProject.objects.filter(project=project, phase=field.phase).first()
                        # if phase_in_project and phase_in_project.completed and field.field.is_milestone:
                        #     phase_in_project.completed = False
                        #     phase_in_project.save()

                    elif rule.action == 'DISABLE':
                        field.active = False

            else:
                # Si la condición no se cumple, desactivar el campo objetivo
                target_field_in_project = FieldsByProject.objects.filter(project=project, field=target_field)
                for field in target_field_in_project:
                    if rule.action == 'ENABLE':
                        field.active = False
                    elif rule.action == 'DISABLE':
                        field.active = True
                    
            field.save()
            toggle_phase_completed(project, field.phase)
    return

def enforce_notifications(list_of_fields, project):
    """
    Aplica las notificaciones si corresponde a los campos modificados de un proyecto.
    """
    input_field_ids = [field.field.id for field in list_of_fields]
    template = project.template

    # Filtrar solo las notificaciones de la plantilla cuyo trigger_field está en los campos de entrada
    notifications = template.notifications.filter(trigger_field__id__in=input_field_ids)

    for notification in notifications:

        # Obtener el valor del campo que dispara la notificación
        field_obj = next((field for field in list_of_fields if field.field.id == notification.trigger_field.id), None)
        if not field_obj:
            continue
        field_value = field_obj.value

        # Evaluar la condición de disparo
        trigger = False
        if notification.trigger_condition == 'IS_FILLED':
            trigger = bool(field_value)
        elif notification.trigger_condition == 'ITS_VALUE_IS':
            trigger = str(field_value) == str(notification.value)
        elif notification.trigger_condition == 'CONTAINS':
            trigger = notification.value in str(field_value)
        elif notification.trigger_condition == 'CHANGES':
            trigger = True  # Asumimos que siempre se dispara si el campo cambia

        if trigger:
            send_notification(project, notification)

    return


def toggle_phase_completed(project, phase):
    """
    Verifica si una fase está completada en un proyecto.
    """
    
    # Use early exit if any milestone field is not completed or cancelled
    incomplete_exists = FieldsByProject.objects.filter(
        project=project,
        phase=phase,
        field__is_milestone=True,
        active=True
    ).exclude(
        status__in=[FieldsByProject.COMPLETED, FieldsByProject.CANCELLED]
    ).exists()

    try:
        phase_in_project = PhasesByProject.objects.get(project=project, phase=phase)
    except PhasesByProject.DoesNotExist:
        return  # Defensive fallback, though this shouldn't happen
    
    if incomplete_exists:
        phase_in_project.completed = False
        phase_in_project.save()
        return  # Not all required fields completed yet

    # Check previous phase
    previous_phase = Phase.objects.filter(order__lt=phase.order).order_by('-order').first()

    if previous_phase:
        try:
            previous_phase_in_project = PhasesByProject.objects.get(project=project, phase=previous_phase)
            if not previous_phase_in_project.completed:
                return  # Cannot complete current phase if previous is not done
        except PhasesByProject.DoesNotExist:
            return  # Defensive fallback

    # All conditions satisfied; mark phase as completed
    phase_in_project.completed = True
    phase_in_project.save()


def init_rules(project):
    """
    Inicializa las reglas de un proyecto.
    """
    template = project.template
    rules = template.rules.all()

    for rule in rules:
        
        if rule.action == 'ENABLE':
            target_field = FieldsByProject.objects.filter(project=project, field=rule.target_field).first()
            if target_field:
                target_field.active = False  # Disable by default
                target_field.save()
    
    return


def populate_users_by_project():
    """
    Función para migrar los datos de usuarios del modelo Project a la nueva tabla UsersByProject.
    Esta función recorre todos los proyectos y crea registros en UsersByProject para cada usuario
    asignado según su rol (Coordinador, Implementador, Implementador 2, Backup Implementador, Incubadora).
    """
    # Mapeo de roles a TypeOfUsers
    role_mapping = {
        'Coordinador': 'coordinator',
        'Implementador': 'implementer1', 
        'Implementador 2': 'implementer2',
        'Backup Implementador': 'backup',
        'Incubadora': 'incubator'
    }
    
    # Obtener o crear los tipos de usuarios
    roles = {}
    for role_name in role_mapping.keys():
        role_obj, created = TypeOfUsers.objects.get_or_create(type_of_user=role_name)
        roles[role_name] = role_obj
        if created:
            print(f"✅ Rol '{role_name}' creado en TypeOfUsers")
    
    # Limpiar registros existentes en UsersByProject (opcional)
    UsersByProject.objects.all().delete()
    print("🗑️ Registros existentes en UsersByProject eliminados")
    
    # Recorrer todos los proyectos
    projects = Project.objects.all()
    created_count = 0
    
    for project in projects:
        print(f"📋 Procesando proyecto: {project.alias}")
        
        # Mapear cada campo del proyecto con su rol correspondiente
        for role_name, field_name in role_mapping.items():
            user = getattr(project, field_name, None)
            
            if user:  # Si el proyecto tiene asignado un usuario para este rol
                try:
                    users_by_project, created = UsersByProject.objects.get_or_create(
                        project=project,
                        user=user,
                        role=roles[role_name],
                        defaults={'is_active': True}
                    )
                    
                    if created:
                        created_count += 1
                        print(f"  ✅ {user.name} asignado como {role_name}")
                    else:
                        print(f"  ⚠️ {user.name} ya existía como {role_name}")
                        
                except Exception as e:
                    print(f"  ❌ Error asignando {user.name} como {role_name}: {str(e)}")
    
    print(f"🎉 Migración completada. {created_count} registros creados en UsersByProject")
    return created_count


def sync_project_users_on_save(project):
    """
    Función para sincronizar la tabla UsersByProject cuando se actualiza un proyecto.
    Esta función debe ser llamada desde una señal post_save del modelo Project.
    """
    role_mapping = {
        'Coordinador': 'coordinator',
        'Implementador': 'implementer1', 
        'Implementador 2': 'implementer2',
        'Backup Implementador': 'backup',
        'Incubadora': 'incubator'
    }
    
    # Obtener roles existentes
    roles = {}
    for role_name in role_mapping.keys():
        try:
            roles[role_name] = TypeOfUsers.objects.get(type_of_user=role_name)
        except TypeOfUsers.DoesNotExist:
            # Crear el rol si no existe
            roles[role_name] = TypeOfUsers.objects.create(type_of_user=role_name)
    
    # Actualizar UsersByProject para este proyecto
    for role_name, field_name in role_mapping.items():
        user = getattr(project, field_name, None)
        role = roles[role_name]
        
        # Eliminar asignaciones anteriores para este rol en este proyecto
        UsersByProject.objects.filter(project=project, role=role).delete()
        
        # Crear nueva asignación si existe usuario
        if user:
            UsersByProject.objects.create(
                project=project,
                user=user,
                role=role,
                is_active=True
            )


def get_project_users_by_role(project, role_name):
    """
    Obtiene todos los usuarios de un proyecto que tienen un rol específico.
    
    Args:
        project: Instancia del modelo Project
        role_name: Nombre del rol (str) como 'Coordinador', 'Implementador', etc.
    
    Returns:
        QuerySet de usuarios (RMOUser) que tienen el rol especificado en el proyecto
    """
    from Projects.models import TypeOfUsers
    from Users.models import RMOUser
    
    try:
        role = TypeOfUsers.objects.get(type_of_user=role_name)
        users_by_project = UsersByProject.objects.filter(
            project=project,
            role=role,
            is_active=True
        ).select_related('user')
        
        return RMOUser.objects.filter(
            id__in=[ubp.user.id for ubp in users_by_project]
        )
        
    except TypeOfUsers.DoesNotExist:
        return RMOUser.objects.none()


def get_all_project_role_assignments(project):
    """
    Obtiene todas las asignaciones de roles para un proyecto específico.
    
    Args:
        project: Instancia del modelo Project
    
    Returns:
        Dict con los roles como claves y listas de usuarios como valores
    """
    assignments = {}
    
    users_by_project = UsersByProject.objects.filter(
        project=project,
        is_active=True
    ).select_related('user', 'role')
    
    for ubp in users_by_project:
        role_name = ubp.role.type_of_user
        if role_name not in assignments:
            assignments[role_name] = []
        
        assignments[role_name].append({
            'id': ubp.user.id,
            'name': ubp.user.name,
            'email': ubp.user.email
        })
    
    return assignments


def validate_user_assignment(project, user, role_name):
    """
    Valida si un usuario está asignado a un rol específico en un proyecto.
    
    Args:
        project: Instancia del modelo Project
        user: Instancia del modelo RMOUser
        role_name: Nombre del rol (str)
    
    Returns:
        bool: True si el usuario está asignado al rol, False en caso contrario
    """
    from Projects.models import TypeOfUsers
    
    try:
        role = TypeOfUsers.objects.get(type_of_user=role_name)
        return UsersByProject.objects.filter(
            project=project,
            user=user,
            role=role,
            is_active=True
        ).exists()
        
    except TypeOfUsers.DoesNotExist:
        return False