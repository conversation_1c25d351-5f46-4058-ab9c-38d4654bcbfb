from rest_framework import serializers
from .models import Contact

class ContactSerializer(serializers.ModelSerializer):
    project_lid = serializers.SerializerMethodField()
    project_company_name = serializers.SerializerMethodField()

    class Meta:
        model = Contact
        fields = ['id', 'name', 'email', 'type', 'position', 'project', 'created_at', 'project_lid', 'project_company_name']

    def get_project_lid(self, obj):
        return obj.project.lid if obj.project else None

    def get_project_company_name(self, obj):
        return obj.project.company_name if obj.project else None
