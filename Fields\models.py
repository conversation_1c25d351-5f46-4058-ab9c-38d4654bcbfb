from django.db import models
from decimal import Decimal

# Create your models here.
class Phase(models.Model):
    name = models.CharField(max_length=100)
    order = models.IntegerField()

    class Meta:
        ordering = ['order']  # Orders by the 'order' field ascending by default

    def __str__(self):
        return self.name


class Subphase(models.Model):
    name = models.CharField(max_length=100)
    phase = models.ForeignKey(Phase, on_delete=models.CASCADE)
    order = models.IntegerField()

    class Meta:
        ordering = ['order']  # Orders by the 'order' field ascending by default

    def save(self, *args, **kwargs):
        if self._state.adding:  # Only set order when creating
            max_order = Subphase.objects.filter(phase=self.phase).aggregate(models.Max('order'))['order__max']
            self.order = (max_order or 0) + 1
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name


class Field(models.Model):
    INFORMATIVE = 'INFORMATIVE'
    SELECTION = 'SELECTION'
    TASK = 'TASK'
    DOCUMENT = 'DOCUMENT'
    TASK_WITH_SUBTASKS = 'TASK_WITH_SUBTASKS'

    FIELD_TYPES = [
        (INFORMATIVE, 'Informative'),
        (SELECTION, 'Selection'),
        (TASK, 'Task'),
        (DOCUMENT, 'Document'),
        (TASK_WITH_SUBTASKS, 'Task with Subtasks')
    ]

    WEIGHT_CHOICES = [
        (Decimal('0'), '0'),
        (Decimal('0.25'), '0.25'),
        (Decimal('0.5'), '0.5'),
        (Decimal('0.75'), '0.75'),
        (Decimal('1'), '1'),
    ]

    name = models.CharField(max_length=255)
    name_en = models.CharField(max_length=255, blank=True)
    description = models.TextField()
    type = models.CharField(max_length=50, choices=FIELD_TYPES)
    subphase = models.ForeignKey(Subphase, on_delete=models.CASCADE)
    weight = models.DecimalField(max_digits=3, decimal_places=2, choices=WEIGHT_CHOICES, default=Decimal('0'))
    is_milestone = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    selection_options = models.JSONField(null=True, blank=True)  # For selection fields
    subtasks = models.JSONField(null=True, blank=True)  # For task fields with subtasks

    def __str__(self):
        return self.name