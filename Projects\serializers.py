# Projects/serializers.py
from rest_framework import serializers
from rest_framework.permissions import IsAuthenticated
from .models import Project, TimestampPrediction, DateField, ImplementationTypes, ProjectDateChangeLog, TypeOfUsers
from django.db import transaction

class TimestampPredictionCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = TimestampPrediction
        fields = ['field', 'value_date']

class TimestampPredictionReadSerializer(serializers.ModelSerializer):
    project_alias = serializers.CharField(source='project.alias', read_only=True)
    field_name = serializers.CharField(source='field.name', read_only=True)
    is_latest = serializers.BooleanField(read_only=True)

    class Meta:
        model = TimestampPrediction
        fields = ['id', 'project', 'project_alias', 'field', 'field_name', 'value_date', 'created_at', 'is_latest']

class DateFieldSerializer(serializers.ModelSerializer):
    class Meta:
        model = DateField
        fields = ['id', 'name', 'is_month_year']

@transaction.atomic
def create_timestamp(project, field, value_date):
    # Marcar registros anteriores como no actuales
    TimestampPrediction.objects.filter(project=project, field=field, is_latest=True).update(is_latest=False)

    # Crear nuevo registro como el actual
    return TimestampPrediction.objects.create(
        project=project,
        field=field,
        value_date=value_date,
        is_latest=True
    )

class ProjectSerializer(serializers.ModelSerializer):
    timestamps = TimestampPredictionCreateSerializer(many=True)

    class Meta:
        model = Project
        fields = [
            'lid', 'alias', 'company_name', 'aggregator', 'implementation_type',
            'implementer1', 'backup', 'coordinator', 'team', 'incubator', 'template',
            'timestamps'
        ]

    def create(self, validated_data):
        timestamps_data = validated_data.pop('timestamps')
        project = Project.objects.create(**validated_data)

        for ts in timestamps_data:
            field = ts['field']
            value_date = ts['value_date']

            # Crear timestamp y marcar como el más reciente
            timestamp = create_timestamp(project, field, value_date)

            # Copiar valor en columna directa del modelo Project (si existe)
            field_name = timestamp.field.name
            if hasattr(project, field_name):
                setattr(project, field_name, timestamp.value_date)

        project.save()
        return project

class ImplementationTypesSerializer(serializers.ModelSerializer):
    class Meta:
        model = ImplementationTypes
        fields = '__all__'


class TypeOfUsersSerializer(serializers.ModelSerializer):
    class Meta:
        model = TypeOfUsers
        fields = ['id', 'type_of_user']

class ProjectDateChangeLogSerializer(serializers.ModelSerializer):
    project = serializers.StringRelatedField()
    phase = serializers.StringRelatedField()
    user = serializers.StringRelatedField()

    class Meta:
        model = ProjectDateChangeLog
        fields = [
            'id', 'project', 'timestamp', 'phase', 'field_type', 'previous_value', 'new_value', 'user', 'comment'
        ]