from django.urls import path
from rest_framework.routers import DefaultRouter

from .views import TemplateCreateView, TemplateListView, TemplateDetailView, TemplateListFilteredView, TemplateToggleActiveView, TemplateUpdateView

router = DefaultRouter()

urlpatterns = [
    path('create/', TemplateCreateView.as_view(), name='template-create'),
    path('list/', TemplateListView.as_view(), name='template-list'),
    path('list/active/', TemplateListFilteredView.as_view(), name='template-list-active'),
    path('detail/<int:pk>/', TemplateDetailView.as_view(), name='template-detail'),
    path('toggle-active/<int:pk>/', TemplateToggleActiveView.as_view(), name='template-toggle-active'),
    path('update-template/<int:pk>/', TemplateUpdateView.as_view(), name='template-update-template'),
]