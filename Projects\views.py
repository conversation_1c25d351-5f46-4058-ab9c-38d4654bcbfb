from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import generics
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from Users.core_db import create_query_rosclar
from .models import Project, DateField, TimestampPrediction, ImplementationTypes, RMOUser, ProjectDateChangeLog, Phase, TypeOfUsers
from .serializers import DateFieldSerializer, TimestampPredictionCreateSerializer
from .serializers import TimestampPredictionReadSerializer, ImplementationTypesSerializer, ProjectDateChangeLogSerializer, TypeOfUsersSerializer
from Users.utilFunctions import permissions_required
from SingleProject.models import FieldsByProject

class DateFieldViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = DateField.objects.all().order_by('id')
    serializer_class = DateFieldSerializer
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')] 

class TimestampPredictionViewSet(viewsets.ModelViewSet):
    queryset = TimestampPrediction.objects.all()
    serializer_class = TimestampPredictionCreateSerializer
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]

class LatestTimestampPredictionListView(generics.ListAPIView):
    """
    Lista solo los timestamps más recientes (is_latest = True)
    """
    queryset = TimestampPrediction.objects.filter(is_latest=True).order_by('-created_at')
    serializer_class = TimestampPredictionReadSerializer
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]

class ClientInfoAPIView(APIView):
    """
    Busca la razón social y el aggregator de una empresa por su LID
    """

    @swagger_auto_schema(
        manual_parameters=[
            openapi.Parameter(
                'lid',
                openapi.IN_QUERY,
                description="LID del proyecto (companies.internal_id)",
                type=openapi.TYPE_STRING,
                required=True
            )
        ]
    )
    @permission_classes([IsAuthenticated, permissions_required('Users.visualizar_proyectos')])
    def get(self, request):
        lid = request.GET.get("lid")
        if not lid:
            return Response({"error": "Falta el LID."}, status=400)
        query = """
            SELECT 
                c.internal_id,                     -- LID
                c.internal_name,                   -- Razón social (client)
                ca.code                            -- Nombre del agregador
            FROM 
                brain_event_store_prod.companies c
            LEFT JOIN 
                brain_event_store_prod.company_aggregators ca
            ON 
                c.company_aggregator_id = ca.id
            WHERE 
                c.internal_id = %s
        """
        try:
            rows = create_query_rosclar(query, params=(lid,), db_name="core")
        except Exception as e:
            return Response({"error": f"Error al consultar la base de datos: {str(e)}"}, status=500)
        if not rows:
            return Response({"error": "LID no encontrado."}, status=404)
        row = rows[0]
        return Response({
            "LID": row[0],
            "client": row[1],
            "aggregator": row[2]
        })

class AllClientsInfoAPIView(APIView):
    """
    Busca la razón social y el aggregator de todas las empresas
    """
    @permission_classes([IsAuthenticated, permissions_required('Users.editar_proyectos')])
    def get(self, request):
        query = """
            SELECT 
                c.internal_id,                     -- LID
                c.internal_name,                   -- Razón social (client)
                ca.code                            -- Nombre del agregador
            FROM 
                brain_event_store_prod.companies c
            LEFT JOIN 
                brain_event_store_prod.company_aggregators ca
            ON 
                c.company_aggregator_id = ca.id
        """
        try:
            rows = create_query_rosclar(query, db_name="core")
        except Exception as e:
            return Response({"error": f"Error al consultar la base de datos: {str(e)}"}, status=500)
        if not rows:
            return Response({"error": "Error al realizar la llamada a la API."}, status=404)
        result = []
        for row in rows:
            result.append({
                "LID": row[0],
                "client": row[1],
                "aggregator": row[2]
            })
        return Response(result)
    

class ImplementationTypesList(generics.ListAPIView):
    """
    Lista los tipos de implementación
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.editar_proyectos')]
    queryset = ImplementationTypes.objects.all()
    serializer_class = ImplementationTypesSerializer


class TypeOfUsersList(generics.ListAPIView):
    """
    Lista los tipos de usuarios
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]
    queryset = TypeOfUsers.objects.all()
    serializer_class = TypeOfUsersSerializer


class AllUsersIncubadoraImplementacion(APIView):
    """
    Devuelve todos los usuarios technicians de los equipos "Incubadora" e "Implementación"
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.editar_proyectos')]

    def get(self, request):
        query = """
            SELECT
                t.name_alias,
                t.email,
                g.id,
                g.name_alias AS group_name_alias
            FROM
                brain_event_store_prod.technicians t
            JOIN
                brain_event_store_prod.technician_groups g ON t.group_id = g.id
            WHERE
                g.name_alias IN ('Incubadora', 'Implementación')
        """
        try:
            rows = create_query_rosclar(query, db_name="core")
        except Exception as e:
            return Response({"error": f"Error al consultar la base de datos: {str(e)}"}, status=500)
        if not rows:
            return Response({"error": "Error al realizar la llamada a la API."}, status=404)
        result = []
        for row in rows:
            try:
                user = RMOUser.objects.filter(email=row[1]).first()
            except Exception:
                user = None
            result.append({
                "user_id": user.id if user else None,
                "name": row[0],
                "email": row[1],
                "group_id": row[2],
                "group_name": row[3],
            })
        return Response(result)

class ProjectDateChangeLogView(APIView):
    """
    Endpoint para registrar un cambio de fecha en un proyecto y actualizar el campo correspondiente.
    """
    
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos', 'Users.editar_proyectos')]
    @swagger_auto_schema(
        operation_summary="Registrar cambio de fecha en un proyecto",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["project_id", "date_field_name","new_value", "phase_id", "comment"],
            properties={
                "project_id": openapi.Schema(type=openapi.TYPE_INTEGER),
                "date_field_name": openapi.Schema(type=openapi.TYPE_STRING),
                "new_value": openapi.Schema(type=openapi.TYPE_STRING, format="date"),
                "phase_id": openapi.Schema(type=openapi.TYPE_INTEGER),
                "comment": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
        responses={
            200: openapi.Response(description="Cambio registrado correctamente"),
            400: openapi.Response(description="Entrada inválida o campo no encontrado"),
            404: openapi.Response(description="Proyecto o fase no encontrado"),
        }
    )
    def post(self, request):
        project_id = request.data.get("project_id")
        date_field_name = request.data.get("date_field_name")
        new_value = request.data.get("new_value")
        phase_id = request.data.get("phase_id")
        comment = request.data.get("comment")
        user = request.user
        if not all([project_id, date_field_name, new_value, phase_id]):
            return Response({"error": "Faltan campos requeridos."}, status=400)
        try:
            project = Project.objects.get(id=project_id)
        except Project.DoesNotExist:
            return Response({"error": "Proyecto no encontrado."}, status=404)
        try:
            phase = Phase.objects.get(id=phase_id)
        except Phase.DoesNotExist:
            return Response({"error": "Fase no encontrada."}, status=404)

        if not hasattr(project, date_field_name):
            return Response({"error": f"El campo {date_field_name} no existe en el modelo Project."}, status=400)
            
        # Validar que el campo de fecha corresponde a la fase especificada
        phase_prefix_mapping = {
            1: "start_",         # START
            2: "collection_",    # COLLECTION
            3: "migration_",     # MIGRATION
            4: "test_",          # TEST
            5: "golive_",        # GO LIVE
            6: "incubadora_"     # INCUBADORA
        }
        
        # Verificar si el campo corresponde a la fase correcta
        expected_prefix = phase_prefix_mapping.get(phase.id)
        if not expected_prefix or not date_field_name.startswith(expected_prefix):
            return Response({
                "error": f"El campo de fecha '{date_field_name}' no corresponde a la fase '{phase.name}'. Debe usar el ID de fase correspondiente."
            }, status=400)
            
        # Obtener el valor anterior antes de hacer el cambio
        previous_value = getattr(project, date_field_name)
        
        # Actualizar el valor
        setattr(project, date_field_name, new_value)
        project.save()
        
        # Crear el registro de cambio con el valor anterior
        log = ProjectDateChangeLog.objects.create(
            project=project,
            phase=phase,
            field_type=date_field_name,
            previous_value=previous_value,
            new_value=new_value,
            user=user,
            comment=comment or ""
        )
        return Response({
            "success": True,
            "message": "Cambio registrado correctamente.",
        })

class ProjectDateChangeLogListView(APIView):
    """
    Lista el historial de cambios de fechas de un proyecto específico.
    """
    serializer_class = ProjectDateChangeLogSerializer

    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]
    @swagger_auto_schema(
        operation_summary="Listar historial de cambios de fechas de un proyecto",
        manual_parameters=[
            openapi.Parameter(
                'project_id',
                openapi.IN_QUERY,
                description="ID del proyecto",
                type=openapi.TYPE_INTEGER,
                required=True
            )
        ],
        responses={
            200: openapi.Response(description="Historial de cambios de fechas del proyecto"),
            404: openapi.Response(description="Proyecto no encontrado"),
        }
    )
    def get(self, request, *args, **kwargs):
        project_id = request.GET.get('project_id')
        if not project_id:
            return Response({'error': 'Falta el parámetro project_id.'}, status=400)
        try:
            project = Project.objects.get(id=project_id)
        except Project.DoesNotExist:
            return Response({'error': 'Proyecto no encontrado.'}, status=404)
        queryset = ProjectDateChangeLog.objects.filter(project=project).order_by('-timestamp')
        serializer = self.serializer_class(queryset, many=True)
        return Response(serializer.data)