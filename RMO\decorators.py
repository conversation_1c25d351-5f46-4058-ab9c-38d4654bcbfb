from functools import wraps
import jwt
from django.conf import settings
from django.contrib.auth import get_user_model
from graphql import GraphQLError

def custom_login_required(resolver):
    @wraps(resolver)
    def wrapper(root, info, *args, **kwargs):
        auth_header = info.context.META.get('HTTP_AUTHORIZATION', '')
        
        if not auth_header.startswith('Bearer '):
            raise GraphQLError("Authentication required: No Bearer token provided")
        
        token = auth_header.replace('Bearer ', '')
        try:
            payload = jwt.decode(
                token,
                settings.GRAPHQL_JWT['JWT_SECRET_KEY'],
                algorithms=[settings.GRAPHQL_JWT['JWT_ALGORITHM']]
            )
            user_id = payload.get('user_id')
            User = get_user_model()
            try:
                user = User.objects.get(id=user_id)
                if not user.is_active:
                    raise GraphQLError("User is inactive")
                info.context.user = user
            except User.DoesNotExist:
                raise GraphQLError("User not found")
        except jwt.ExpiredSignatureError:
            raise GraphQLError("Token has expired")
        except jwt.InvalidTokenError as e:
            raise GraphQLError(f"Invalid token: {str(e)}")
        
        return resolver(root, info, *args, **kwargs)
    
    return wrapper