from rest_framework import viewsets, status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.viewsets import ModelViewSet
from rest_framework_simplejwt.views import TokenObtainPairView
from Users.BBDD_conn import import_users_from_core

from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema

from django.contrib.auth.models import Group, Permission

from .models import RMOUser
from .serializers import UserSerializer, AssignUserRoleSerializer, CustomTokenObtainPairSerializer
from .core_db import create_query_rosclar
from .models import RMOUser
from Team.models import Team
from .utilFunctions import get_custom_permissions, permissions_required

class UserViewSet(ModelViewSet):
    """
    ViewSet para manejar los usuarios del sistema. Permite listar, crear, actualizar y eliminar usuarios.
    """
    queryset = RMOUser.objects.all()
    serializer_class = UserSerializer

    @swagger_auto_schema(tags=['users'])
    @permission_classes([permissions_required('Users.visualizar_usuarios')])
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(tags=['users'])
    @permission_classes([permissions_required('Users.visualizar_usuarios')])
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(tags=['users'])
    @permission_classes([permissions_required('Users.visualizar_usuarios', 'Users.editar_usuarios')])
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(tags=['users'])
    @permission_classes([permissions_required('Users.visualizar_usuarios', 'Users.editar_usuarios')])
    def update(self, request, *args, **kwargs):
        return super().update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['users'])
    @permission_classes([permissions_required('Users.visualizar_usuarios', 'Users.editar_usuarios')])
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(tags=['users'])
    @permission_classes([permissions_required('Users.visualizar_usuarios', 'Users.editar_usuarios')])
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)


class MeView(APIView):
    """
    Vista para obtener y actualizar el perfil del usuario autenticado.
    """
    swagger_tags = ['users']

    @swagger_auto_schema(
        operation_summary="Obtener datos del usuario autenticado",
        responses={200: openapi.Response("Datos del usuario")}
    )
    @permission_classes([IsAuthenticated, permissions_required('Users.visualizar_usuarios')])
    def get(self, request):
        serializer = UserSerializer(request.user)
        return Response(serializer.data)
    
    @swagger_auto_schema(
        operation_summary="Actualizar perfil del usuario autenticado",
        request_body=UserSerializer,
        responses={200: openapi.Response("Perfil actualizado")}
    )
    @permission_classes([IsAuthenticated, permissions_required('Users.visualizar_usuarios', 'Users.editar_usuarios')])
    def put(self, request):
        user = request.user
        serializer = UserSerializer(user, data=request.data, partial=True, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=400)



@api_view(['GET'])
def test_bbdd_connection(request):
    """
    Endpoint para probar la conexión a la base de datos externa.
    """
    try:
        result = create_query_rosclar("SELECT name_alias, email FROM brain_event_store_prod.technicians LIMIT 5", db_name="core")
        return Response({"status": "ok", "sample": result})
    except Exception as e:
        return Response({"status": "error", "message": str(e)}, status=500)
    

@api_view(['POST'])
@permission_classes([IsAuthenticated, permissions_required('Users.visualizar_usuarios', 'Users.editar_usuarios')])
def sync_with_core(request):
    """
    Sincroniza los usuarios desde la base de datos externa CORE.
    """
    try:
        import_users_from_core()
        return Response({
            "status": "ok",
            "message": "Usuarios sincronizados correctamente desde CORE."
        })
    except Exception as e:
        return Response({
            "status": "error",
            "message": str(e)
        }, status=500)
    


class AssignUserRoleView(APIView):
    """
    Endpoint para asignar un rol a un usuario.
    """

    @swagger_auto_schema(
        operation_summary="Asignar un rol a un usuario",
        request_body=AssignUserRoleSerializer,
        responses={
            200: openapi.Response(description="Rol asignado correctamente"),
            400: openapi.Response(description="Error de validación"),
        }
    )

    @permission_classes([IsAuthenticated, permissions_required('Users.visualizar_usuarios', 'Users.editar_usuarios')])
    def post(self, request):
        serializer = AssignUserRoleSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response({"detail": "Role assigned successfully."}, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class RoleViewSet(viewsets.ViewSet):
    """
    ViewSet para manejar los roles de usuario.
    """

    # Creación de un nuevo rol
    @swagger_auto_schema(
        operation_summary="Crear un nuevo rol",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["name", "permissions"],
            properties={
                "name": openapi.Schema(type=openapi.TYPE_STRING),
                "permissions": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Items(type=openapi.TYPE_INTEGER)
                )
            },
        ),
        responses={201: openapi.Response("Role created")}
    )
    @permission_classes([IsAuthenticated, permissions_required('Users.visualizar_usuarios', 'Users.editar_usuarios')])
    def create(self, request):
        name = request.data.get("name")
        permissions = request.data.get("permissions", [])

        if not name:
            return Response({"error": "El nombre del rol es obligatorio."}, status=status.HTTP_400_BAD_REQUEST)

        if Group.objects.filter(name=name).exists():
            return Response({"error": "Ya existe un rol con este nombre."}, status=status.HTTP_400_BAD_REQUEST)

        group = Group.objects.create(name=name)
        if permissions:
            try:
                group.permissions.set(Permission.objects.filter(id__in=permissions))
            except Permission.DoesNotExist:
                return Response({"error": "Alguno de los permisos especificados no existe."}, status=status.HTTP_400_BAD_REQUEST)

        return Response({
            "id": group.id,
            "name": group.name,
            "permissions": list(group.permissions.values("id", "name", "codename"))
        }, status=status.HTTP_201_CREATED)


    # Listar todos los roles con sus permisos
    @swagger_auto_schema(
    operation_summary="Listar todos los roles con sus permisos",
    method="get",
    responses={200: openapi.Response("List of roles with permissions")}
    )
    @action(detail=False, methods=["get"])
    @permission_classes([IsAuthenticated, permissions_required('Users.visualizar_usuarios')])
    def list_roles(self, request):
        all_permissions = get_custom_permissions()

        roles = Group.objects.all()
        roles_data = []

        for group in roles:
            assigned_permissions = group.permissions.all()
            permissions_data = [
                {
                    "id": perm.id,
                    "name": perm.name,
                    "codename": perm.codename,
                    "checked": True
                }
                for perm in assigned_permissions
            ]

            roles_data.append({
                "id": group.id,
                "name": group.name,
                "permissions": permissions_data
            })

        return Response(roles_data, status=status.HTTP_200_OK)

    # Listar todos los permisos
    @swagger_auto_schema(
        operation_summary="Listar todos los permisos",
        method="get",
        responses={200: openapi.Response("List of permissions")}
    )
    @action(detail=False, methods=["get"])
    @permission_classes([IsAuthenticated, permissions_required('Users.visualizar_usuarios')])
    def list_permissions(self, request):
        permissions = get_custom_permissions()
        data = [
            {
                "id": perm.id,
                "name": perm.name,
                "codename": perm.codename
            }
            for perm in permissions
        ]
        return Response(data, status=status.HTTP_200_OK)


    # Listar permisos de un rol por ID
    @swagger_auto_schema(
        operation_summary="Listar permisos de un rol por ID",
        method="get",
        responses={200: openapi.Response("Permissions for role")}
    )
    @action(detail=True, methods=["get"], url_path="permissions")
    @permission_classes([IsAuthenticated, permissions_required('Users.visualizar_usuarios')])
    def role_permissions(self, request, pk=None):
        try:
            group = Group.objects.get(pk=pk)
        except Group.DoesNotExist:
            return Response({"error": "Rol no encontrado."}, status=status.HTTP_404_NOT_FOUND)

        assigned_permissions = group.permissions.all()

        response_data = [
            {
                "id": perm.id,
                "name": perm.name,
                "codename": perm.codename,
                "checked": True
            }
            for perm in assigned_permissions
        ]

        return Response({
            "role": group.name,
            "permissions": response_data
        })


    # Actualizar permisos de un rol
    @swagger_auto_schema(
        operation_summary="Actualizar permisos de un rol",
        method="post",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "permissions": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Items(type=openapi.TYPE_INTEGER)
                )
            }
        ),
        responses={200: openapi.Response(description="Role permissions updated")}
    )
    @action(detail=True, methods=["post"])
    @permission_classes([IsAuthenticated, permissions_required('Users.visualizar_usuarios', 'Users.editar_usuarios')])
    def update_permissions(self, request, pk=None):
        try:
            group = Group.objects.get(pk=pk)
        except Group.DoesNotExist:
            return Response({"error": "Rol no encontrado."}, status=404)
        permission_ids = request.data.get("permissions", [])
        try:
            group.permissions.set(Permission.objects.filter(id__in=permission_ids))
        except Permission.DoesNotExist:
            return Response({"error": "Alguno de los permisos especificados no existe."}, status=status.HTTP_400_BAD_REQUEST)
        return Response({"success": True, "message": "Permisos actualizados."})

    # Eliminar un rol
    @swagger_auto_schema(
        operation_summary="Eliminar un rol",
        method="delete",
        responses={204: openapi.Response(description="Role deleted")}
    )

    @action(detail=True, methods=["delete"])
    @permission_classes([IsAuthenticated, permissions_required('Users.visualizar_usuarios', 'Users.editar_usuarios')])
    def delete(self, request, pk=None):
        try:
            group = Group.objects.get(pk=pk)
        except Group.DoesNotExist:
            return Response({"error": "Rol no encontrado."}, status=404)

        group.delete()
        return Response({"success": "Rol eliminado correctamente."}, status=status.HTTP_204_NO_CONTENT)
    

class ListRecipients(APIView):
    """
    API endpoint to list all possible recipients of notifications.
    Devuelve todos los usuarios, todos los equipos y todos los líderes de equipo (con el equipo del que son líderes).
    """

    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_config', 'Users.editar_config')]
    @swagger_auto_schema(
        operation_summary="Listar todos los posibles destinatarios de notificaciones",
        responses={200: openapi.Response(
            description="Lista de usuarios, equipos y líderes de equipo",
            examples={
                "application/json": {
                    "users": [
                        {"id": 1, "name": "Juan Pérez", "email": "<EMAIL>"}
                    ],
                    "teams": [
                        {"id": 1, "name": "Equipo A"}
                    ],
                    "leaders": [
                        {"id": 2, "name": "Ana Líder", "email": "<EMAIL>", "team": {"id": 1, "name": "Equipo A"}}
                    ]
                }
            }
        )}
    )
    def get(self, request):
        users = [
            {"id": user.id, "name": user.name, "email": user.email}
            for user in RMOUser.objects.all()
        ]

        # Todos los equipos
        teams = [
            {"id": team.id, "name": team.name}
            for team in Team.objects.all()
        ]

        # Todos los líderes de equipo (is_leader=True)
        leaders = []
        for user in RMOUser.objects.filter(is_leader=True):
            team = user.team
            leaders.append({
                "id": user.id,
                "name": user.name,
                "email": user.email,
                "team": {"id": team.id, "name": team.name} if team else None
            })

        return Response({
            "users": users,
            "teams": teams,
            "leaders": leaders
        })

class CustomTokenObtainPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer



