# Generated by Django 3.2.25 on 2025-07-17 09:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Projects', '0011_typeofusers'),
        ('SingleProject', '0006_alter_fieldsbyproject_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='UsersByProject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='users_by_project', to='Projects.project')),
                ('role', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='users_by_role', to='Projects.typeofusers')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='projects_by_user', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Usuario por Proyecto',
                'verbose_name_plural': 'Usuarios por Proyecto',
                'unique_together': {('project', 'user', 'role')},
            },
        ),
    ]
