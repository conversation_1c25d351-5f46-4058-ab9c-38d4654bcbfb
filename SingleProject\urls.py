from django.urls import path
from .views import (
    InitSingleProject, 
    ListFieldsByPhase, 
    ListCommentsByPhase, 
    AddComment, 
    BulkUpdateFieldsView, 
    VerifyPhaseView,
    PendingFieldsView,
    ChangeProjectTemplateView,
    ProjectEmailsHistoryView,
    UsersByProjectListView,
    ProjectUsersListView,
    UserProjectsListView,
    ProjectUserDetailView
)

urlpatterns = [
    path('<int:project_id>/', InitSingleProject.as_view(), name='init-single-project'),
    path('<int:project_id>/phases/<int:phase_id>/fields/', ListFieldsByPhase.as_view(), name='list-fields-by-phase'),
    path('<int:project_id>/phases/<int:phase_id>/fields/update/', BulkUpdateFieldsView.as_view(), name='bulk-update-fields'),
    path('<int:project_id>/phases/<int:phase_id>/verify/', VerifyPhaseView.as_view(), name='verify-phase'),
    path('<int:project_id>/phases/<int:phase_id>/comments/', ListCommentsByPhase.as_view(), name='list-comments-by-phase'),
    path('<int:project_id>/phases/<int:phase_id>/comments/add/', AddComment.as_view(), name='add-comment'),
    path('<int:project_id>/pending-fields/', PendingFieldsView.as_view(), name='pending-fields'),
    path('<int:project_id>/change-template/', ChangeProjectTemplateView.as_view(), name='change-project-template'),
    path('<int:project_id>/emails/', ProjectEmailsHistoryView.as_view(), name='project-emails-history'),
    
    # URLs para UsersByProject
    path('users-by-project/', UsersByProjectListView.as_view(), name='users-by-project-list'),
    path('projects/users/', ProjectUsersListView.as_view(), name='project-users-grouped'),
    path('users/projects/', UserProjectsListView.as_view(), name='user-projects-grouped'),
    path('users-by-project/<int:pk>/', ProjectUserDetailView.as_view(), name='project-user-detail'),
]
