import graphene
from django.db import models
from django.apps import apps
from graphene import Enum
from graphene_django.types import DjangoObjectType
from graphql_jwt.decorators import permission_required

from RMO.decorators import custom_login_required
from .models import Notification
from Fields.models import Field
from Users.models import RMOUser
from Fields.schema import FieldType
from Emails.models import EmailTemplate

# ============================== CLASSES DECLARATION ============================== #

class NotificationType(DjangoObjectType):
    class Meta:
        model = Notification
        fields = "__all__"
    
    user_recipients = graphene.List(lambda: graphene.String)
    team_recipients = graphene.List(lambda: graphene.String)
    leader_recipients = graphene.List(lambda: graphene.String)
    role_recipients = graphene.List(lambda: graphene.String)
    all_recipients = graphene.List(lambda: graphene.String)
    trigger_field = graphene.Field(FieldType)

    def resolve_user_recipients(self, info):
        return [user.email for user in self.user_recipients.all()]

    def resolve_team_recipients(self, info):
        return [user.email for user in self.team_recipients.all()]

    def resolve_leader_recipients(self, info):
        return [user.email for user in self.leader_recipients.all()]

    def resolve_role_recipients(self, info):
        return [role.type_of_user for role in self.role_recipients.all()]

    def resolve_all_recipients(self, info):
        return [user.email for user in self.all_recipients.all()]

    def resolve_trigger_field(self, info):
        return self.trigger_field

class NotificationInput(graphene.InputObjectType):
    name = graphene.String()
    name_en = graphene.String()
    description = graphene.String()
    description_en = graphene.String()
    trigger_field_id = graphene.ID(required=True)
    user_recipients = graphene.List(graphene.String)
    team_recipients = graphene.List(graphene.String)
    leader_recipients = graphene.List(graphene.String)
    role_recipients = graphene.List(graphene.String)
    trigger_condition = graphene.String(required=True)
    value = graphene.String()
    email_template_id = graphene.ID(required=True)

# ============================== QUERIES DECLARATIONS ============================== #

class Query(graphene.ObjectType):
    all_notifications = graphene.List(NotificationType)
    notification = graphene.Field(NotificationType, id=graphene.Int(required=True))

    @custom_login_required
    def resolve_all_notifications(self, info):
        return Notification.objects.all()

    @custom_login_required
    def resolve_notification(self, info, id):
        return Notification.objects.get(id=id)
    

# ============================== MUTATIONS DECLARATIONS ============================== #

class CreateNotification(graphene.Mutation):
    class Arguments:
        notification_data = NotificationInput(required=True)

    notification = graphene.Field(NotificationType)

    @custom_login_required
    def mutate(self, info, notification_data):
        try:
            trigger_field = Field.objects.get(id=notification_data.trigger_field_id)
        except Field.DoesNotExist:
            raise Exception("El campo asociado no existe.")
        try:
            from Emails.models import EmailTemplate
            email_template = EmailTemplate.objects.get(id=notification_data.email_template_id)
        except Exception:
            raise Exception("La plantilla de email no existe.")
        # Recipients
        user_recipients = []
        team_recipients = []
        leader_recipients = []
        role_recipients = []
        team_users = []
        role_users = []
        if hasattr(notification_data, 'user_recipients') and notification_data.user_recipients:
            user_recipients = list(RMOUser.objects.filter(id__in=notification_data.user_recipients))
        if hasattr(notification_data, 'team_recipients') and notification_data.team_recipients:
            # team_recipients is a list of team IDs
            from Team.models import Team
            teams = Team.objects.filter(id__in=notification_data.team_recipients)
            for team in teams:
                team_users.extend(list(RMOUser.objects.filter(team=team)))
        if hasattr(notification_data, 'leader_recipients') and notification_data.leader_recipients:
            leader_recipients = list(RMOUser.objects.filter(id__in=notification_data.leader_recipients))
        if hasattr(notification_data, 'role_recipients') and notification_data.role_recipients:
            TypeOfUsers = apps.get_model('Projects', 'TypeOfUsers')
            role_recipients = list(TypeOfUsers.objects.filter(id__in=notification_data.role_recipients))
            # Get users by role from projects
            for role in role_recipients:
                role_name = role.type_of_user.lower()
                if role_name == 'coordinador':
                    role_users.extend(list(RMOUser.objects.filter(coordinated_projects1__isnull=False).distinct()))
                elif role_name == 'implementador':
                    role_users.extend(list(RMOUser.objects.filter(
                        models.Q(implemented_projects_1__isnull=False) | 
                        models.Q(implemented_projects_2__isnull=False)
                    ).distinct()))
                elif role_name == 'implementador 2':
                    role_users.extend(list(RMOUser.objects.filter(implemented_projects_2__isnull=False).distinct()))
                elif role_name == 'backup implementador':
                    role_users.extend(list(RMOUser.objects.filter(backup_projects__isnull=False).distinct()))
                elif role_name == 'incubadora':
                    role_users.extend(list(RMOUser.objects.filter(incubated_projects__isnull=False).distinct()))
        
        # All recipients = union of all
        all_recipients_set = set(user_recipients + team_users + leader_recipients + role_users)
        notification = Notification(
            name=notification_data.name,
            name_en=notification_data.name_en if hasattr(notification_data, 'name_en') else None,
            description=notification_data.description,
            description_en=notification_data.description_en if hasattr(notification_data, 'description_en') else None,
            trigger_field=trigger_field,
            trigger_condition=notification_data.trigger_condition,
            value=notification_data.value,
            email_template=email_template
        )
        notification.save()
        notification.user_recipients.set(user_recipients)
        notification.team_recipients.set(team_users)
        notification.leader_recipients.set(leader_recipients)
        notification.role_recipients.set(role_recipients)
        notification.all_recipients.set(all_recipients_set)
        return CreateNotification(notification=notification)

class UpdateNotification(graphene.Mutation):
    class Arguments:
        id = graphene.ID(required=True)
        name = graphene.String()
        name_en = graphene.String()
        description = graphene.String()
        description_en = graphene.String()
        trigger_condition = graphene.String()
        value = graphene.String()
        user_recipients = graphene.List(graphene.String)
        team_recipients = graphene.List(graphene.String)
        leader_recipients = graphene.List(graphene.String)
        role_recipients = graphene.List(graphene.String)
        email_template_id = graphene.ID()
        trigger_field_id = graphene.ID()

    notification = graphene.Field(NotificationType)

    @custom_login_required
    def mutate(self, info, id, name=None, name_en=None, description=None, description_en=None, trigger_condition=None, value=None, user_recipients=None, team_recipients=None, leader_recipients=None, role_recipients=None, email_template_id=None, trigger_field_id=None):
        try:
            notification = Notification.objects.get(id=id)
        except Notification.DoesNotExist:
            raise Exception("La notificación con este ID no existe.")
        has_template = notification.templates.exists() if hasattr(notification, 'templates') else False
        # Always editable fields
        if name is not None:
            notification.name = name
        if name_en is not None:
            notification.name_en = name_en
        if description is not None:
            notification.description = description
        if description_en is not None:
            notification.description_en = description_en
        if trigger_condition is not None:
            notification.trigger_condition = trigger_condition
        if value is not None:
            notification.value = value
        if email_template_id is not None:
            try:
                email_template = EmailTemplate.objects.get(id=email_template_id)
                notification.email_template = email_template
            except EmailTemplate.DoesNotExist:
                raise Exception("La plantilla de email no existe.")
        # Only allow trigger_field update if not related to any template
        if not has_template and trigger_field_id is not None:
            
            try:
                trigger_field = Field.objects.get(id=trigger_field_id)
                notification.trigger_field = trigger_field
            except Field.DoesNotExist:
                raise Exception("El campo de trigger especificado no existe.")
        # Recipients
        user_objs = notification.user_recipients.all() if user_recipients is None else RMOUser.objects.filter(id__in=user_recipients)
        team_users = []
        if team_recipients is None:
            team_users = list(notification.team_recipients.all())
        else:
            from Team.models import Team
            teams = Team.objects.filter(id__in=team_recipients)
            for team in teams:
                team_users.extend(list(RMOUser.objects.filter(team=team)))
        leader_objs = notification.leader_recipients.all() if leader_recipients is None else RMOUser.objects.filter(id__in=leader_recipients)
        
        # Handle role recipients
        TypeOfUsers = apps.get_model('Projects', 'TypeOfUsers')
        role_objs = notification.role_recipients.all() if role_recipients is None else TypeOfUsers.objects.filter(id__in=role_recipients)
        role_users = []
        if role_recipients is not None:
            for role in role_objs:
                role_name = role.type_of_user.lower()
                if role_name == 'coordinador':
                    role_users.extend(list(RMOUser.objects.filter(coordinated_projects1__isnull=False).distinct()))
                elif role_name == 'implementador':
                    role_users.extend(list(RMOUser.objects.filter(
                        models.Q(implemented_projects_1__isnull=False) | 
                        models.Q(implemented_projects_2__isnull=False)
                    ).distinct()))
                elif role_name == 'implementador 2':
                    role_users.extend(list(RMOUser.objects.filter(implemented_projects_2__isnull=False).distinct()))
                elif role_name == 'backup implementador':
                    role_users.extend(list(RMOUser.objects.filter(backup_projects__isnull=False).distinct()))
                elif role_name == 'incubadora':
                    role_users.extend(list(RMOUser.objects.filter(incubated_projects__isnull=False).distinct()))
        else:
            # If role_recipients is None, get users from existing role_recipients
            for role in notification.role_recipients.all():
                role_name = role.type_of_user.lower()
                if role_name == 'coordinador':
                    role_users.extend(list(RMOUser.objects.filter(coordinated_projects1__isnull=False).distinct()))
                elif role_name == 'implementador':
                    role_users.extend(list(RMOUser.objects.filter(
                        models.Q(implemented_projects_1__isnull=False) | 
                        models.Q(implemented_projects_2__isnull=False)
                    ).distinct()))
                elif role_name == 'implementador 2':
                    role_users.extend(list(RMOUser.objects.filter(implemented_projects_2__isnull=False).distinct()))
                elif role_name == 'backup implementador':
                    role_users.extend(list(RMOUser.objects.filter(backup_projects__isnull=False).distinct()))
                elif role_name == 'incubadora':
                    role_users.extend(list(RMOUser.objects.filter(incubated_projects__isnull=False).distinct()))
        
        notification.user_recipients.set(user_objs)
        notification.team_recipients.set(team_users)
        notification.leader_recipients.set(leader_objs)
        notification.role_recipients.set(role_objs)
        # All recipients = union of all
        all_recipients_set = set(list(user_objs) + list(team_users) + list(leader_objs) + list(role_users))
        notification.all_recipients.set(all_recipients_set)
        notification.save()
        return UpdateNotification(notification=notification)
    
class DeleteNotification(graphene.Mutation):
    class Arguments:
        id = graphene.ID(required=True)

    ok = graphene.Boolean()

    @custom_login_required
    def mutate(self, info, id):
        try:
            notification = Notification.objects.get(id=id)
            notification.delete()
            return DeleteNotification(ok=True)
        except Notification.DoesNotExist:
            raise Exception("La notificación con este ID no existe.")

class Mutation(graphene.ObjectType):
    create_notification = CreateNotification.Field()
    update_notification = UpdateNotification.Field()
    delete_notification = DeleteNotification.Field()