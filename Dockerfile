FROM python:3.12-slim

WORKDIR /code

# Install essential build tools and dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    python3-dev \
    pkg-config \
    default-libmysqlclient-dev \
    libffi-dev \
    && rm -rf /var/lib/apt/lists/*

RUN pip install pdfkit jinja2 cryptography

# Install wkhtmltopdf
RUN apt-get update && apt-get install -y \
    wkhtmltopdf \
    && rm -rf /var/lib/apt/lists/*

# Set the XDG_RUNTIME_DIR environment variable
ENV XDG_RUNTIME_DIR /tmp/runtime-root

# Install setuptools first (contains pkg_resources)
RUN pip install --upgrade pip setuptools wheel

# Install requirements
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000