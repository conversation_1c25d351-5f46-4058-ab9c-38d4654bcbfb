from email.utils import localtime
import threading
from django.shortcuts import render
from django.utils.timezone import now
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, generics
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import permission_classes
from django.db.models import Prefetch

from Users.utilFunctions import permissions_required
from Projects.models import Project, DateField
from SingleProject.utilFunctions import compute_phase_percentage, compute_subphase_percentage, update_field_status, toggle_phase_completed
from SingleProject.models import FieldsByProject, PhasesByProject, Comment, UsersByProject
from SingleProject.serializers import UsersByProjectSerializer, UsersByProjectDetailSerializer, ProjectUsersGroupedSerializer, UserProjectsGroupedSerializer
from Fields.models import Phase, Subphase
from SingleProject.utilFunctions import enforce_rules, enforce_notifications, init_rules
from Templates.models import Template
from Notifications.models import NotificationLog

class InitSingleProject(APIView):
    """
    Endpoint para mandar la info inicial y general del proyecto.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]
    @swagger_auto_schema(
        operation_summary="Obtiene toda la información general de un proyecto",
        responses={
            200: "Información general del proyecto obtenida correctamente",
            404: "Proyecto no encontrado"
        }
    )
    def get(self, request, project_id):
        try:
            project = Project.objects.get(id=project_id)
        except Project.DoesNotExist:
            return Response({'error': 'Proyecto no encontrado.'}, status=404)

        # Datos generales del proyecto (sin fechas)
        project_main_data = {
            'id': project.id,
            'lid': project.lid,
            'alias': project.alias,
            'company_name': project.company_name,
            'aggregator': project.aggregator,
            'implementation_type': project.implementation_type,
            'implementer1': project.implementer1.name if project.implementer1 else None,
            'implementer2': project.implementer2.name if project.implementer2 else None,
            'backup': project.backup.name if project.backup else None,
            'coordinator': project.coordinator.name if project.coordinator else None,
            'team': project.team.name if project.team else None,
            'incubator': project.incubator.name if project.incubator else None,
            'template': project.template.name if project.template else None,
            'status': project.status,
            'created_at': project.created_at,
        }
        # Fechas del proyecto
        project_dates = {
            'start_initial_date': project.start_initial_date,
            'start_final_date': project.start_final_date,
            'start_real_initial_date': project.start_real_initial_date,
            'start_real_final_date': project.start_real_final_date,
            'collection_initial_date': project.collection_initial_date,
            'collection_final_date': project.collection_final_date,
            'collection_real_initial_date': project.collection_real_initial_date,
            'collection_real_final_date': project.collection_real_final_date,
            'migration_initial_date': project.migration_initial_date,
            'migration_final_date': project.migration_final_date,
            'migration_real_initial_date': project.migration_real_initial_date,
            'migration_real_final_date': project.migration_real_final_date,
            'test_initial_date': project.test_initial_date,
            'test_final_date': project.test_final_date,
            'test_real_initial_date': project.test_real_initial_date,
            'test_real_final_date': project.test_real_final_date,
            'month1_test': project.month1_test,
            'month2_test': project.month2_test,
            'golive_initial_date': project.golive_initial_date,
            'golive_final_date': project.golive_final_date,
            'golive_real_initial_date': project.golive_real_initial_date,
            'golive_real_final_date': project.golive_real_final_date,
            'incubadora_initial_date': project.incubadora_initial_date,
            'incubadora_final_date': project.incubadora_final_date,
            'incubadora_real_initial_date': project.incubadora_real_initial_date,
            'incubadora_real_final_date': project.incubadora_real_final_date,
        }

        # Fases y subfases
        phases = PhasesByProject.objects.filter(project=project).order_by('phase__order')
        phases_data = []
        for phase in phases:
            phase_percentage = compute_phase_percentage(project, phase.phase)

            subphases = Subphase.objects.filter(phase=phase.phase).order_by('order')

            subphases_data = []
            for subphase in subphases:
                subphase_percentage = compute_subphase_percentage(project, subphase.phase, subphase)
                subphases_data.append({
                    'id': subphase.id,
                    'name': subphase.name,
                    'order': subphase.order,
                    'percentage': subphase_percentage,
                })

            phases_data.append({
                'id': phase.id,
                'name': phase.phase.name,
                'order': phase.phase.order,
                'percentage': phase_percentage,
                'verified': phase.verified,
                'completed': phase.completed,
                'subphases': subphases_data,
            })


        return Response({
            'project': project_main_data,
            'dates': project_dates,
            'phases': phases_data,
        })


class ListFieldsByPhase(APIView):
    """
    Endpoint para listar los campos de una fase específica de un proyecto.
    """

    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]
    @swagger_auto_schema(
        operation_summary="Listar campos de una fase específica de un proyecto",
        responses={
            200: "Campos de la fase obtenidos correctamente",
            404: "Proyecto o fase no encontrada"
        }
    )
    def get(self, request, project_id, phase_id):
        try:
            project = Project.objects.get(id=project_id)
        except Project.DoesNotExist:
            return Response({'error': 'Proyecto no encontrado.'}, status=404)

        try:
            phase = Phase.objects.get(id=phase_id)
        except Phase.DoesNotExist:
            return Response({'error': 'Fase no encontrada.'}, status=404)
        
        try:
            phase_by_project = PhasesByProject.objects.get(project=project, phase=phase)
        except PhasesByProject.DoesNotExist:
            return Response({'error': 'Fase del proyecto no encontrada.'}, status=404)
        
        fields = FieldsByProject.objects.filter(project=project, phase=phase)
        fields_data = []
        for field in fields:
            fields_data.append({
                'id': field.id,
                'name': field.field.name,
                'type': field.field.type,
                'value': field.value,
                'status': field.status,
                'is_active': field.active,
                'observations': field.observations,
                'is_milestone': field.field.is_milestone,
                'weight': field.field.weight,
                'subphase': field.field.subphase.name if field.field.subphase else None,
                'selection_options': field.field.selection_options if field.field.type == 'SELECTION' else None,
                'subtask': field.field.subtasks if field.field.type == 'TASK_WITH_SUBTASKS' else None,
            })

        phase_info = {
            'id': phase.id,
            'name': phase.name,
            'verified': phase_by_project.verified,
            'completed': phase_by_project.completed,
            'percentage': compute_phase_percentage(project, phase),
        }

        subphases_info = []
        for subphase in Subphase.objects.filter(phase=phase).order_by('order'):
            subphase_percentage = compute_subphase_percentage(project, phase, subphase)
            subphases_info.append({
                'id': subphase.id,
                'name': subphase.name,
                'order': subphase.order,
                'percentage': subphase_percentage,
            })

        return Response({
            'fields': fields_data,
            'phase': phase_info,
            'subphases': subphases_info
        })
    

class ListCommentsByPhase(APIView):
    """
    Endpoint para listar los comentarios de una fase específica de un proyecto.
    """

    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]
    @swagger_auto_schema(
        operation_summary="Listar comentarios de una fase específica de un proyecto",
        responses={
            200: "Comentarios de la fase obtenidos correctamente",
            404: "Proyecto o fase no encontrada"
        }
    )
    def get(self, request, project_id, phase_id):
        try:
            project = Project.objects.get(id=project_id)
        except Project.DoesNotExist:
            return Response({'error': 'Proyecto no encontrado.'}, status=404)

        try:
            phase = Phase.objects.get(id=phase_id)
        except Phase.DoesNotExist:
            return Response({'error': 'Fase no encontrada.'}, status=404)

        try:
            phase_by_project = PhasesByProject.objects.get(project=project, phase=phase)
            comments = phase_by_project.comments.all()
            
            if not comments:
                return Response({'comments': []})

            comments_data = []
            for comment in comments:
                comments_data.append({
                    'id': comment.id,
                    'user': comment.user.name,
                    'text': comment.text,
                    'created_at': localtime(comment.created_at).strftime('%d/%m/%Y %H:%M')
                })

            return Response({
                'comments': comments_data,
            })
        except PhasesByProject.DoesNotExist:
            return Response({'comments': []})


class AddComment(APIView):
    """
    Endpoint para añadir un comentario a una fase de un proyecto.
    """

    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos', 'Users.editar_proyectos')]
    @swagger_auto_schema(
        operation_summary="Añadir comentario a una fase de un proyecto",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["text"],
            properties={
                "text": openapi.Schema(type=openapi.TYPE_STRING),
            },
        ),
        responses={
            200: openapi.Response(description="Comentario añadido correctamente"),
            400: openapi.Response(description="Entrada inválida o campo no encontrado"),
        }
    )
    def post(self, request, project_id, phase_id):
        text = request.data.get("text")
        user = request.user

        if not text:
            return Response({"error": "Faltan campos requeridos."}, status=status.HTTP_400_BAD_REQUEST)
        
        phasebyproject = PhasesByProject.objects.filter(project_id=project_id, phase_id=phase_id).first()
        if not phasebyproject:
            return Response({"error": "Fase del proyecto no encontrada."}, status=status.HTTP_400_BAD_REQUEST)

        comment = Comment.objects.create(
            user=user,
            text=text
        )
        
        phasebyproject.comments.add(comment)
        phasebyproject.save()
        
        return Response({
            "success": True,
            "message": "Comentario añadido correctamente.",
        })


class BulkUpdateFieldsView(APIView):
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos', 'Users.editar_proyectos')]

    @swagger_auto_schema(
        operation_summary="Actualizar múltiples campos de una fase de un proyecto",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["fields"],
            properties={
                "fields": openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        properties={
                            "field_id": openapi.Schema(type=openapi.TYPE_INTEGER),
                            "value": openapi.Schema(type=openapi.TYPE_STRING),
                            "observations": openapi.Schema(type=openapi.TYPE_STRING),
                        },
                        required=["field_id", "value"]
                    )
                ),
            },
        ),
        responses={
            200: openapi.Response(description="Campos actualizados correctamente"),
            400: openapi.Response(description="Entrada inválida o campo no encontrado"),
        }
    )
    def post(self, request, project_id, phase_id):
        fields = request.data.get("fields")
        if not project_id or not phase_id or not fields:
            return Response({"error": "Faltan campos requeridos."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            project = Project.objects.get(id=project_id)
            phase = Phase.objects.get(id=phase_id)
        except Project.DoesNotExist:
            return Response({"error": "Proyecto no encontrado."}, status=status.HTTP_404_NOT_FOUND)
        except Phase.DoesNotExist:
            return Response({"error": "Fase no encontrada."}, status=status.HTTP_404_NOT_FOUND)

        all_fields = FieldsByProject.objects.filter(project_id=project_id, phase_id=phase_id)
        fields_dict = {f.id: f for f in all_fields}

        # Update project real date if needed
        if all(f.status == FieldsByProject.NOT_STARTED for f in all_fields):
            real_datefield = DateField.objects.filter(phase=phase, name__icontains="real").first()
            if real_datefield:
                setattr(project, real_datefield.name, now())
                project.save()

        updated_fields = []
        missing_fields = []

        for field_data in fields:
            field_id = field_data.get("field_id")
            value = field_data.get("value")
            observation = field_data.get("observations", "")

            field = fields_dict.get(field_id)
            if not field:
                missing_fields.append(field_id)
                continue

            field.value = value
            field.observations = observation
            field.save()

            update_field_status(field)
            updated_fields.append(field)

        if missing_fields:
            return Response({"error": f"Campos no encontrados: {missing_fields}"}, status=status.HTTP_404_NOT_FOUND)

        # Post-processing
        enforce_rules(updated_fields, project)
        toggle_phase_completed(project, phase)

        # Run enforce_notifications in the background
        threading.Thread(target=enforce_notifications, args=(updated_fields, project)).start()

        return Response({
            "success": True,
            "message": "Actualización completada.",
            "updated_field_ids": [f.id for f in updated_fields]
        })


class VerifyPhaseView(APIView):
    """
    Endpoint para verificar una fase de un proyecto.
    """

    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos', 'Users.editar_proyectos')]
    @swagger_auto_schema(
        operation_summary="Verificar una fase de un proyecto",
        responses={
            200: openapi.Response(description="Fase verificada correctamente"),
            400: openapi.Response(description="Entrada inválida o fase no encontrada"),
        }
    )
    def post(self, request, project_id, phase_id):

        if not project_id or not phase_id:
            return Response({"error": "Faltan campos requeridos."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            phase_by_project = PhasesByProject.objects.get(project_id=project_id, phase_id=phase_id)
            phase_by_project.verified = True
            phase_by_project.save()
            return Response({"success": True, "message": "Fase verificada correctamente."})
        except PhasesByProject.DoesNotExist:
            return Response({"error": "Fase del proyecto no encontrada."}, status=status.HTTP_404_NOT_FOUND)

class PendingFieldsView(APIView):
    """
    Endpoint to retrieve all fields across all phases with status NOT_STARTED or IN_PROGRESS.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]
    
    @swagger_auto_schema(
        operation_summary="Obtiene todos los campos pendientes o en progreso de un proyecto",
        responses={
            200: "Campos pendientes obtenidos correctamente",
            404: "Proyecto no encontrado"
        }
    )
    def get(self, request, project_id):
        try:
            project = Project.objects.get(id=project_id)
        except Project.DoesNotExist:
            return Response({'error': 'Proyecto no encontrado.'}, status=status.HTTP_404_NOT_FOUND)
        
        # Get all fields that are either NOT_STARTED or IN_PROGRESS
        pending_fields = FieldsByProject.objects.filter(
            project=project, 
            status__in=[FieldsByProject.NOT_STARTED, FieldsByProject.IN_PROGRESS]
        )
        
        # Format the response
        result = []
        for field in pending_fields:
            field_data = {
                'phase_id': field.phase.id,
                'phase_name': field.phase.name,
                'field_id': field.id,
                'field_name': field.field.name,
                'description': field.field.description,
                'status': field.status,
                'subphase_id': field.field.subphase.id,
                'subphase_name': field.field.subphase.name,
                'type': field.field.type,
            }
            
            # Include value if it exists
            if field.value:
                field_data['value'] = field.value
            
            result.append(field_data)
        
        return Response(result)

class ChangeProjectTemplateView(APIView):
    """
    Endpoint para cambiar la plantilla de un proyecto, solo si el proyecto no ha comenzado.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.editar_proyectos')]

    @swagger_auto_schema(
        operation_summary="Cambia la plantilla de un proyecto si todos los campos están en estado NOT_STARTED",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['template_id'],
            properties={
                'template_id': openapi.Schema(type=openapi.TYPE_INTEGER, description='ID de la nueva plantilla')
            }
        ),
        responses={
            200: "Plantilla cambiada correctamente",
            400: "Error en los datos enviados o el proyecto ya ha comenzado",
            404: "Proyecto o plantilla no encontrados"
        }
    )
    def post(self, request, project_id):
        # Verificar que el proyecto existe
        try:
            project = Project.objects.get(id=project_id)
        except Project.DoesNotExist:
            return Response({"error": "El proyecto no existe"}, status=status.HTTP_404_NOT_FOUND)

        # Obtener la nueva plantilla
        template_id = request.data.get('template_id')
        if not template_id:
            return Response({"error": "Se requiere el ID de la nueva plantilla"}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            new_template = Template.objects.get(id=template_id)
        except Template.DoesNotExist:
            return Response({"error": "La plantilla no existe"}, status=status.HTTP_404_NOT_FOUND)
        
        # Verificar que todos los campos del proyecto están en estado NOT_STARTED
        fields = FieldsByProject.objects.filter(project=project)
        
        if not fields.exists():
            # Si no hay campos, podemos cambiar la plantilla directamente
            pass
        else:
            # Verificar que todos los campos están en NOT_STARTED
            for field in fields:
                if field.status != FieldsByProject.NOT_STARTED:
                    return Response(
                        {"error": "No se puede cambiar la plantilla porque el proyecto ya ha comenzado"},
                        status=status.HTTP_400_BAD_REQUEST
                    )
        
        # Eliminar los campos y fases actuales del proyecto
        FieldsByProject.objects.filter(project=project).delete()
        PhasesByProject.objects.filter(project=project).delete()
        
        # Actualizar la plantilla del proyecto
        old_template = project.template
        project.template = new_template
        project.save()
        
        # Inicializar los campos según la nueva plantilla
        if new_template:
            # Crear los campos según la nueva plantilla
            for field in new_template.fields.all():
                FieldsByProject.objects.create(
                    project=project,
                    field=field,
                    phase=field.subphase.phase,
                    value=None,
                    observations=None,
                    active=True,
                    status=FieldsByProject.NOT_STARTED
                )
            
            # Crear o actualizar las fases del proyecto
            for phase in Phase.objects.all():
                PhasesByProject.objects.create(
                    project=project,
                    phase=phase,
                    verified=False,
                    completed=False
                )
            
            # Inicializar las reglas del proyecto con la nueva plantilla
            init_rules(project)
        
        return Response({
            "success": True,
            "message": f"Plantilla cambiada correctamente de '{old_template.name if old_template else 'Ninguna'}' a '{new_template.name}'",
            "project_id": project.id,
            "new_template_id": new_template.id,
            "fields_created": new_template.fields.count() if new_template else 0
        })

class ProjectEmailsHistoryView(APIView):
    """
    Endpoint para listar el historial de correos enviados de un proyecto específico.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]

    @swagger_auto_schema(
        operation_summary="Listar historial de correos enviados de un proyecto",
        operation_description="Devuelve una lista con todos los correos enviados relacionados con un proyecto específico",
        responses={
            200: "Lista de correos enviados",
            404: "Proyecto no encontrado"
        }
    )
    def get(self, request, project_id):
        # Verificar que el proyecto existe
        try:
            project = Project.objects.get(id=project_id)
        except Project.DoesNotExist:
            return Response({"error": "Proyecto no encontrado"}, status=status.HTTP_404_NOT_FOUND)
            
        # Obtener el historial de correos enviados
        logs = NotificationLog.objects.filter(project_id=project_id).order_by('-timestamp')
        
        emails = [
            {
                'id': log.id,
                'notification_name': log.notification.name,
                'notification_description': log.notification.description,
                'timestamp': log.timestamp,
                'subject': log.subject,
                'recipients': log.recipients,
                'content': log.email,
            }
            for log in logs
        ]


# === NUEVAS VISTAS PARA UsersByProject ===

class UsersByProjectListView(generics.ListAPIView):
    """
    Vista para listar todos los usuarios asignados a proyectos.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]
    serializer_class = UsersByProjectSerializer
    
    def get_queryset(self):
        queryset = UsersByProject.objects.select_related('project', 'user', 'role').filter(is_active=True)
        
        # Filtros opcionales
        project_id = self.request.query_params.get('project_id', None)
        user_id = self.request.query_params.get('user_id', None)
        role_id = self.request.query_params.get('role_id', None)
        
        if project_id:
            queryset = queryset.filter(project_id=project_id)
        if user_id:
            queryset = queryset.filter(user_id=user_id)
        if role_id:
            queryset = queryset.filter(role_id=role_id)
            
        return queryset

    @swagger_auto_schema(
        operation_summary="Obtiene la lista de usuarios asignados a proyectos",
        manual_parameters=[
            openapi.Parameter('project_id', openapi.IN_QUERY, description="ID del proyecto", type=openapi.TYPE_INTEGER),
            openapi.Parameter('user_id', openapi.IN_QUERY, description="ID del usuario", type=openapi.TYPE_INTEGER),
            openapi.Parameter('role_id', openapi.IN_QUERY, description="ID del rol", type=openapi.TYPE_INTEGER),
        ],
        responses={200: UsersByProjectSerializer(many=True)}
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)


class ProjectUsersListView(APIView):
    """
    Vista para obtener todos los usuarios agrupados por proyecto.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]

    @swagger_auto_schema(
        operation_summary="Obtiene usuarios agrupados por proyecto",
        responses={200: "Lista de proyectos con sus usuarios asignados"}
    )
    def get(self, request):
        # Obtener proyectos con sus usuarios
        projects_with_users = {}
        
        users_by_project = UsersByProject.objects.select_related(
            'project', 'user', 'role'
        ).filter(is_active=True).order_by('project__alias', 'role__type_of_user')
        
        for ubp in users_by_project:
            project_id = ubp.project.id
            if project_id not in projects_with_users:
                projects_with_users[project_id] = {
                    'project_id': project_id,
                    'project_alias': ubp.project.alias,
                    'project_company': ubp.project.company_name,
                    'users': []
                }
            
            projects_with_users[project_id]['users'].append(
                UsersByProjectSerializer(ubp).data
            )
        
        return Response(list(projects_with_users.values()), status=status.HTTP_200_OK)


class UserProjectsListView(APIView):
    """
    Vista para obtener todos los proyectos agrupados por usuario.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]

    @swagger_auto_schema(
        operation_summary="Obtiene proyectos agrupados por usuario",
        responses={200: "Lista de usuarios con sus proyectos asignados"}
    )
    def get(self, request):
        # Obtener usuarios con sus proyectos
        users_with_projects = {}
        
        users_by_project = UsersByProject.objects.select_related(
            'project', 'user', 'role'
        ).filter(is_active=True).order_by('user__name', 'project__alias')
        
        for ubp in users_by_project:
            user_id = ubp.user.id
            if user_id not in users_with_projects:
                users_with_projects[user_id] = {
                    'user_id': user_id,
                    'user_name': ubp.user.name,
                    'user_email': ubp.user.email,
                    'projects': []
                }
            
            users_with_projects[user_id]['projects'].append(
                UsersByProjectSerializer(ubp).data
            )
        
        return Response(list(users_with_projects.values()), status=status.HTTP_200_OK)


class ProjectUserDetailView(generics.RetrieveAPIView):
    """
    Vista para obtener el detalle de un usuario específico en un proyecto.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]
    serializer_class = UsersByProjectDetailSerializer
    queryset = UsersByProject.objects.select_related('project', 'user', 'role')

    @swagger_auto_schema(
        operation_summary="Obtiene el detalle de un usuario asignado a un proyecto",
        responses={200: UsersByProjectDetailSerializer()}
    )
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)
        
        return Response(emails, status=status.HTTP_200_OK)