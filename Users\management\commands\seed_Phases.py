from django.core.management.base import BaseCommand
from Fields.models import Phase

class Command(BaseCommand):
    help = 'Seeds the Phase table with predefined elements'

    def handle(self, *args, **options):
        # Define the elements to insert (example data)
        predefined_fields = [
            {'name': 'START', "order": 1},
            {'name': 'COLLECTION', "order": 2},
            {'name': 'MIGRATION', "order": 3},
            {'name': 'TEST', "order": 4},
            {'name': 'GO LIVE', "order": 5},
            {'name': 'INCUBADORA', "order": 6}
        ]

        # Insert elements if they don't exist
        for field_data in predefined_fields:
            if not Phase.objects.filter(name=field_data['name']).exists():
                Phase.objects.create(**field_data)

        self.stdout.write(self.style.SUCCESS('✅ Fases añadidas correctamente'))