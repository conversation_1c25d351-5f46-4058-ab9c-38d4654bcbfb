from django.db import models

from Fields.models import Field

class Report(models.Model):
    name = models.Char<PERSON>ield(max_length=255)
    name_en = models.Char<PERSON>ield(max_length=255)
    description = models.CharField(max_length=255)
    description_en = models.CharField(max_length=255)
    document_name = models.CharField(max_length=255)
    document_name_en = models.CharField(max_length=255)
    observations = models.BooleanField(default=False)
    filters = models.JSONField(default=dict, blank=True)
    phases = models.BooleanField(default=False)
    subphases = models.BooleanField(default=False)

    fields = models.ManyToManyField(Field, related_name='reports', blank=True)
    
    active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name