from django.core.management.base import BaseCommand
from django.db import connection
from Team.models import Team
from Users.BBDD_conn import get_all_users_core

class Command(BaseCommand):
    help = 'Limpia la tabla Team, resetea los IDs y la rellena con los grupos únicos desde la BBDD externa'

    def handle(self, *args, **kwargs):
        # Paso 1: Borrar todos los teams
        Team.objects.all().delete()
        self.stdout.write(self.style.WARNING('⚠ Todos los registros en Team han sido eliminados.'))

        # Paso 2: Resetear AUTOINCREMENT (solo funciona en SQLite)
        with connection.cursor() as cursor:
            cursor.execute("DELETE FROM sqlite_sequence WHERE name='Team_team';")
        self.stdout.write(self.style.WARNING('♻ Contador de IDs reseteado a 1.'))

        # Paso 3: Obtener grupos únicos desde la BBDD externa
        rows = get_all_users_core()
        unique_groups = set()

        for name, email, group_name, group_code, is_leader, rn in rows:
            unique_key = f"{group_name} ({group_code})"
            if unique_key not in unique_groups:
                unique_groups.add(unique_key)

        # Paso 4: Crear Teams nuevos usando clave combinada
        for unique_key in unique_groups:
            group_name, group_code = unique_key.split(' (')
            group_code = group_code.rstrip(')')
            Team.objects.create(name=group_name, code=f"{group_name}_{group_code}")
            self.stdout.write(f"✅ Creado Team: {group_name} ({group_code}) → code: {group_name}_{group_code}")

        self.stdout.write(self.style.SUCCESS(f"\n🎉 Equipos creados: {len(unique_groups)}"))
