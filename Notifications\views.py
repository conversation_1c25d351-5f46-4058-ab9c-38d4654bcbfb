from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from drf_yasg.utils import swagger_auto_schema

from Users.utilFunctions import permissions_required
from .models import NotificationLog

class ProjectEmailsListView(APIView):
    """
    View to list all emails sent related to a project.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]

    @swagger_auto_schema(
        operation_description="List all emails related to a project",
        responses={200: "List of emails"},
        deprecated=False
    )
    def get(self, request, project_id):
        logs = NotificationLog.objects.filter(project_id=project_id).order_by('-timestamp')
        emails = [
            {
                'id': log.id,
                'notification_name': log.notification.name,
                'notification_description': log.notification.description,
                'timestamp': log.timestamp,
                'subject': log.subject,
                'recipients': log.recipients,
                'content': log.email,
            }
            for log in logs
        ]
        return Response(emails, status=200)
    