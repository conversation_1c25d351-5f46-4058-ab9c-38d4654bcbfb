from django.utils import timezone
from django.db import models
from Users.models import RMOUser
from Team.models import Team
from Fields.models import Phase
from Templates.models import Template
# class Team(models.Model):
#     team_name = models.Char<PERSON>ield(max_length=255)

#     def __str__(self):
#         return self.team_name


class Project(models.Model):
    lid = models.CharField(max_length=50, unique=True)
    alias = models.Char<PERSON>ield(max_length=50, unique=True)
    company_name = models.CharField(max_length=255)
    aggregator = models.CharField(max_length=255, null=True, blank=True)
    implementer1 = models.ForeignKey(RMOUser, null=True, blank=True, related_name="implemented_projects_1", on_delete=models.SET_NULL)
    implementer2 = models.ForeignKey(RMOUser, null=True, blank=True, related_name="implemented_projects_2", on_delete=models.SET_NULL)
    backup = models.ForeignKey(RMOUser, null=True, blank=True, related_name="backup_projects", on_delete=models.SET_NULL)
    coordinator = models.ForeignKey(RMOUser, null=True, blank=True, related_name="coordinated_projects1", on_delete=models.SET_NULL)
    team = models.ForeignKey(Team, null=True, blank=True, on_delete=models.SET_NULL)
    incubator = models.ForeignKey(RMOUser, null=True, blank=True, related_name="incubated_projects", on_delete=models.SET_NULL)
    template = models.ForeignKey(Template, null=True, blank=True, on_delete=models.SET_NULL)

    start_initial_date = models.DateField(null=True, blank=True)
    start_final_date = models.DateField(null=True, blank=True)
    start_real_initial_date = models.DateField(null=True, blank=True)
    start_real_final_date = models.DateField(null=True, blank=True)

    collection_initial_date = models.DateField(null=True, blank=True)
    collection_final_date = models.DateField(null=True, blank=True)
    collection_real_initial_date = models.DateField(null=True, blank=True)
    collection_real_final_date = models.DateField(null=True, blank=True)
    
    migration_initial_date = models.DateField(null=True, blank=True)
    migration_final_date = models.DateField(null=True, blank=True)
    migration_real_initial_date = models.DateField(null=True, blank=True)
    migration_real_final_date = models.DateField(null=True, blank=True)

    test_initial_date = models.DateField(null=True, blank=True)
    test_final_date = models.DateField(null=True, blank=True)
    test_real_initial_date = models.DateField(null=True, blank=True)
    test_real_final_date = models.DateField(null=True, blank=True)

    month1_test = models.DateField(null=True, blank=True)
    month2_test = models.DateField(null=True, blank=True)

    golive_initial_date = models.DateField(null=True, blank=True)
    golive_final_date = models.DateField(null=True, blank=True)
    golive_real_initial_date = models.DateField(null=True, blank=True)
    golive_real_final_date = models.DateField(null=True, blank=True) # mes live
    
    incubadora_initial_date = models.DateField(null=True, blank=True)
    incubadora_final_date = models.DateField(null=True, blank=True)
    incubadora_real_initial_date = models.DateField(null=True, blank=True)
    incubadora_real_final_date = models.DateField(null=True, blank=True)

    IN_PROGRESS = 'IN_PROGRESS'
    ON_HOLD = 'ON_HOLD'
    CANCELLED = 'CANCELLED'
    SCHEDULED = 'SCHEDULED'
    COMPLETED = 'COMPLETED'

    STATUS_CHOICES = [
        (IN_PROGRESS, 'In Progress'),
        (ON_HOLD, 'On Hold'),
        (CANCELLED, 'Cancelled'),
        (SCHEDULED, 'Scheduled'),
        (COMPLETED, 'Completed'),
    ]

    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default=SCHEDULED)

    EXISTENTE = 'EXISTENTE'
    MIGRACION = 'MIGRACIÓN'
    NUEVA = 'NUEVA'
    SUBROGACION = 'SUBROGACIÓN'

    IMPLEMENTATION_TYPES_CHOICES = [
        (EXISTENTE, 'Existente'),
        (MIGRACION, 'Migración'),
        (NUEVA, 'Nueva'),
        (SUBROGACION, 'Subrogación'),
    ]
    
    implementation_type = models.CharField(max_length=255, choices=IMPLEMENTATION_TYPES_CHOICES)
    created_at = models.DateTimeField(auto_now_add=True, null=True, blank=True)
    
    def __str__(self):
        return f"{self.alias}"
        

class DateField(models.Model):
    name = models.CharField(max_length=100, unique=True)
    is_month_year = models.BooleanField(default=False)
    phase = models.ForeignKey(Phase, on_delete=models.CASCADE, related_name="date_fields")

    def __str__(self):
        return self.name


class TimestampPrediction(models.Model):
    project = models.ForeignKey(Project, related_name="timestamps", on_delete=models.CASCADE)
    field = models.ForeignKey(DateField, on_delete=models.CASCADE)
    value_date = models.DateField()
    created_at = models.DateTimeField(auto_now_add=True)
    is_latest = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.project.alias} - {self.field.name}: {self.value_date}"


class ImplementationTypes(models.Model):
    name = models.CharField(max_length=100, unique=True)
    
    def __str__(self):
        return self.name


class TypeOfUsers(models.Model):
    type_of_user = models.CharField(max_length=100, unique=True)
    
    def __str__(self):
        return self.type_of_user

class ProjectDateChangeLog(models.Model):
    project = models.ForeignKey("Project", on_delete=models.CASCADE, related_name="date_change_logs")
    timestamp = models.DateTimeField(default=timezone.now)
    phase = models.ForeignKey(Phase, on_delete=models.SET_NULL, null=True, blank=True)
    field_type = models.CharField(max_length=100)
    previous_value = models.DateField(null=True, blank=True)
    new_value = models.DateField(null=True, blank=True)
    user = models.ForeignKey(RMOUser, on_delete=models.SET_NULL, null=True, blank=True)
    comment = models.TextField(blank=True)

    class Meta:
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.project.alias} - {self.phase} ({self.field_type}) changed on {self.timestamp}"
