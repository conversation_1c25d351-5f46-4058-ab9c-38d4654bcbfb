from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import DateFieldViewSet, TimestampPredictionViewSet
from .views import ClientInfoAPIView, TypeOfUsersList, LatestTimestampPredictionListView, AllClientsInfoAPIView, ImplementationTypesList, AllUsersIncubadoraImplementacion, ProjectDateChangeLogView, ProjectDateChangeLogListView
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated

router = DefaultRouter()
# router.register(r'proyectos', ProjectViewSet, basename='proyecto')
router.register(r'date-fields', DateFieldViewSet, basename='datefield')
router.register(r'timestamps', TimestampPredictionViewSet, basename='timestamp')

urlpatterns = [
    path('client-by-lid/', ClientInfoAPIView.as_view(), name='client-by-lid'),
    path('all-clients-info/', AllClientsInfoAPIView.as_view(), name='all-clients-info'),
    path('implementation-types/', ImplementationTypesList.as_view(), name='implementation-types'),
    path('type-of-users/', TypeOfUsersList.as_view(), name='type-of-users'),
    path('all-users-incubadora-implementacion/', AllUsersIncubadoraImplementacion.as_view(), name='all-users-incubadora-implementacion'),
    path('timestamps/latest/', LatestTimestampPredictionListView.as_view(), name='latest-timestamps'),
    path('datechange/', ProjectDateChangeLogView.as_view(), name='datechange'),
    path('datechange/logs/', ProjectDateChangeLogListView.as_view(), name='datechange-logs'),
    path('', include(router.urls)),
]
