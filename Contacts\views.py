from django.shortcuts import render
from rest_framework import generics, status
from rest_framework.response import Response
from rest_framework.exceptions import NotFound, ValidationError
from rest_framework.views import APIView
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework.permissions import IsAuthenticated

from Users.utilFunctions import permissions_required
from .models import Contact
from .serializers import ContactSerializer
from Projects.models import Project

class ContactListCreateView(generics.ListCreateAPIView):
    queryset = Contact.objects.all()
    serializer_class = ContactSerializer

    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_contactos')]
    @swagger_auto_schema(operation_description="Lista y crea contactos")
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_contactos', 'Users.editar_contactos')]
    @swagger_auto_schema(operation_description="Crea un nuevo contacto")
    def post(self, request, *args, **kwargs):
        try:
            return super().post(request, *args, **kwargs)
        except ValidationError as e:
            return Response({'detalle': 'Datos inválidos', 'errores': e.detail}, status=status.HTTP_400_BAD_REQUEST)

class ContactRetrieveUpdateDestroyView(generics.RetrieveUpdateDestroyAPIView):
    queryset = Contact.objects.all()
    serializer_class = ContactSerializer

    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_contactos')]
    @swagger_auto_schema(operation_description="Obtiene un contacto por ID")
    def get(self, request, *args, **kwargs):
        try:
            return super().get(request, *args, **kwargs)
        except NotFound:
            return Response({'detalle': 'Contacto no encontrado'}, status=status.HTTP_404_NOT_FOUND)

    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_contactos', 'Users.editar_contactos')]
    @swagger_auto_schema(operation_description="Actualiza un contacto por ID")
    def put(self, request, *args, **kwargs):
        try:
            return super().put(request, *args, **kwargs)
        except NotFound:
            return Response({'detalle': 'Contacto no encontrado'}, status=status.HTTP_404_NOT_FOUND)
        except ValidationError as e:
            return Response({'detalle': 'Datos inválidos', 'errores': e.detail}, status=status.HTTP_400_BAD_REQUEST)

    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_contactos', 'Users.editar_contactos')]
    @swagger_auto_schema(operation_description="Elimina un contacto por ID")
    def delete(self, request, *args, **kwargs):
        try:
            return super().delete(request, *args, **kwargs)
        except NotFound:
            return Response({'detalle': 'Contacto no encontrado'}, status=status.HTTP_404_NOT_FOUND)

class ProjectContactsListView(APIView):
    serializer_class = ContactSerializer

    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_contactos')]
    @swagger_auto_schema(operation_description="Lista todos los contactos de un proyecto dado su ID", manual_parameters=[
        openapi.Parameter(
            'project_id',
            openapi.IN_QUERY,
            description="ID del proyecto",
            type=openapi.TYPE_INTEGER,
            required=True
        )
    ])
    def get(self, request, *args, **kwargs):
        project_id = request.GET.get('project_id')
        if not project_id:
            return Response({'detalle': 'Falta el parámetro project_id.'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            Project.objects.get(id=project_id)
        except Project.DoesNotExist:
            return Response({'detalle': 'Proyecto no encontrado.'}, status=status.HTTP_404_NOT_FOUND)
        queryset = Contact.objects.filter(project_id=project_id)
        serializer = self.serializer_class(queryset, many=True)
        return Response(serializer.data)
