/* --- <PERSON>GE SETUP --- */
@page {
  size: A4 landscape;
  margin: 2cm 1cm 2cm 1cm;
  @top-center {
    content: element(header);
  }
  @bottom-center {
    content: element(footer);
  }
}

/* --- RUNNING HEADER & FOOTER --- */

.header {
  position: running(header);
  height: 2cm;
  padding: 0 1.5rem;
  font-size: 0.75rem;
  font-weight: bold;
  max-width: 1000px;
  margin: 0 auto;
  opacity: 0.7;
}

/* Remove borders from header table */
.header table,
.header table td {
  border-top: none !important;
  border-right: none !important;
  border-left: none !important;
  border-color: rgba(128, 128, 128, 0.671);
  width: 100%;
}

.header-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between !important;
  width: 100%;
}

.header-content .logo {
  margin-right: auto;
}

.header-content .slogan {
  margin-left: auto;
  text-align: right;
  font-size: 12px;
  font-weight: 800;
  text-align: right;
  color: #b1b3ae;
}

.footer {
position: running(footer);
display: block;
height: 3.5cm;
text-align: initial;
color: #7c7c7c;
opacity: 0.7;
max-width: 1000px;
margin-top: 1.5rem !important;
margin-bottom: 0.5rem !important;
margin: 0 auto;
}

.footer p {
  white-space: nowrap;
  font-size: 0.45rem;
}

.footer img {
  width: 100px;
  height: auto;
  vertical-align: middle;
}

/* Remove borders from footer table */
.footer table,
.footer table td {
  border: none !important;
  /* font-size: 0.2rem; */
}

.triangle-footer {
  position: absolute;
  left: -4rem;
  bottom: -3rem;
  width: 70px !important;
  height: auto;
  z-index: 1;
  opacity: 0.7;
}


/* --- LOGO --- */
.logo {
  width: 140px;
  height: auto;
}

/* --- FONT --- */
body {
  font-family: 'Montserrat', sans-serif;
  color: #333;
  background: white;
  margin: 0;
  padding: 0;
  font-size: 10px;
  width: 100%;
  min-height: 100vh;
}

/* Montserrat Font - Local Files */
@font-face {
  font-family: 'Montserrat';
  src: url('file:///C:/.../Montserrat-Regular.ttf') format('truetype');
  font-weight: 400;
}

/* Add other font-face as needed... */

/* --- GENERAL LAYOUT --- */
.container {
  width: 100%;
  padding: 0;
  background: white;
  padding-bottom: 100px;
  margin-bottom: 1rem;
}

/* --- COVER PAGE --- */
.cover-page {
  position: relative;
  width: 100%;
  height: 100vh;
  max-width: 1000px;
  margin: 0 auto;
  page-break-after: always;
  page: cover;
  background: white;
  padding-top: 8cm;
}

.cover-content {
  position: relative;
  width: 100%;
  height: 100%;
}

.cover-header {
  position: absolute;
  top: 1cm;
  right: 3rem;
  text-align: right;
  z-index: 2;
  padding-bottom: 3rem !important;
}

.cover-header h1 {
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
}

.cover-header .subtitle {
  font-size: 1.65rem;
  color: #01b0d7;
  margin: 0.5rem 0 0 0;
  text-transform: uppercase;
  font-weight: 600;
}

.cover-footer {
  position: absolute;
  top: 5.5cm;
  left: 0rem;
  z-index: 2;
}

.cover-footer .logo {
  width: 250px;
  display: block;
  margin-bottom: 1rem;
  margin-left: 3rem;
  left: 1rem;
  bottom: 1rem;
}

.cover-footer .timestamp {
  font-size: 1rem;
  margin: 0;
  padding: 0;
  padding-left: 6rem;
}

.triangle {
  position: absolute;
  left: -4rem;
  bottom: -26rem;
  width: 120px;
  height: auto;
  z-index: 1;
}

@page cover {
  @top-center {
    content: none;
  }
  @bottom-center {
    content: none;
  }
}

/* --- REPORT PAGE --- */
.report-page {
  padding: 0 1rem 1rem 1rem;
  page-break-after: always;
}

.report-header {
  display: flex;
  justify-content: space-between;
  margin-top: 0;
  margin-bottom: 0;
}

.report-header .info-block {
  width: 48%;
  font-size: 0.8rem;
}

.report-header .info-block h3 {
  margin-bottom: 0.4rem;
}
.report-header .info-block p {
  margin-top: 0;
}

.report-header h3 {
  font-size: 1.2rem;
  font-weight: 600;
  position: relative;
  display: inline-block;
}

.report-header h3::after, .report-body .report-title::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #00b0d6;
}

.report-title {
  font-size: 1.4rem;
  font-weight: 600;
  text-transform: uppercase;
  margin-top: 1rem;
  position: relative;
  display: inline-block;
  margin-bottom: 0.2rem;
}

.report-desc {
  font-size: 0.9rem;
  margin-bottom: 1rem;
  /* font-weight: 600; */
  color: rgba(169, 169, 169, 1);
}

/* --- TABLES --- */
table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 1rem;
  font-size: 0.8rem;
  page-break-inside: auto;
}

thead {
  display: table-header-group;
  page-break-inside: avoid;
}

tfoot {
  display: table-footer-group;
}

tr {
  page-break-inside: avoid;
  page-break-after: auto;
}

td, th {
  border: 1px solid black;
  padding: 0.2rem 0.3rem;
  text-align: left;
  font-size: 12px;
}

tr.field-header th {
  background-color: #dfdede;
  page-break-inside: avoid;
}

.phase-header {
  background-color: rgba(35, 37, 38, 1);
  color: white;
  font-weight: bold;
  font-size: 0.9rem;
}

.subphase-header {
  background-color: rgba(0, 175, 215, 1);
  font-weight: bold;
  color: white;
}

.status-cell {
  text-align: center;
  vertical-align: middle;
}

.status-dot {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  border-radius: 50%;
}
.status-completed { background: #3bb143; }
.status-inprogress { background: #ff9800; }
.status-notstarted { background: #e53935; }

/* --- COLUMN WIDTH --- */
.dynamic-table.has-observations th:nth-child(1),
.dynamic-table.has-observations td:nth-child(1) { width: 25%; }
.dynamic-table.has-observations th:nth-child(2),
.dynamic-table.has-observations td:nth-child(2) { width: 4%; }
.dynamic-table.has-observations th:nth-child(3),
.dynamic-table.has-observations td:nth-child(3) { width: 30%; }
.dynamic-table.has-observations th:nth-child(4),
.dynamic-table.has-observations td:nth-child(4) { width: 41%; }

.dynamic-table:not(.has-observations) th:nth-child(1),
.dynamic-table:not(.has-observations) td:nth-child(1) { width: 35%; }
.dynamic-table:not(.has-observations) th:nth-child(2),
.dynamic-table:not(.has-observations) td:nth-child(2) { width: 4%; }
.dynamic-table:not(.has-observations) th:nth-child(3),
.dynamic-table:not(.has-observations) td:nth-child(3) { width: 61%; }

