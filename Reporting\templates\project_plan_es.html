<!DOCTYPE html>
<html lang="es" xmlns:mso="urn:schemas-microsoft-com:office:office" xmlns:msdt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>{{ filename }}</title>
        {% if css_path %}
        <link rel="stylesheet" href="{{ css_path }}">
        {% endif %}
<!--[if gte mso 9]><xml>
<mso:CustomDocumentProperties>
<mso:_ApprovalStatus msdt:dt="string">0</mso:_ApprovalStatus>
<mso:_ApprovalAssignedTo msdt:dt="string"></mso:_ApprovalAssignedTo>
<mso:_ApprovalRespondedBy msdt:dt="string"></mso:_ApprovalRespondedBy>
</mso:CustomDocumentProperties>
</xml><![endif]-->
</head>
    <body>
        <div class="container">
            <header>
                <div>
                    <img src="{{ logo_path }}" alt="ROSCLAR Logo" class="logo" />
                </div>
                <div class="title">
                    PROJECT PLAN
                </div>
                <div class="slogan">
                    "YOU WILL NEVER <br> WORK ALONE"
                </div>
            </header>

            <section class="implementacion">
                <div class="implementacion-section">
                    <h2 class="title-section">
                        DATOS IMPLEMENTACIÓN
                    </h2>
                    <table class="datos-implementacion-table">
                        <tr class="datos-implementacion-item">
                            <td
                                class="datos-implementacion-item-title">Empresa:</td>
                            <td class="datos-implementacion-item-value">{{ project.company_name }}</td>
                        </tr>
                        <tr class="datos-implementacion-item">
                            <td
                                class="datos-implementacion-item-title">Agregador:</td>
                            <td class="datos-implementacion-item-value">{{ project.aggregator }}</td>
                        </tr>
                        <tr class="datos-implementacion-item">
                            <td
                                class="datos-implementacion-item-title">Tipo:</td>
                            <td class="datos-implementacion-item-value">{{ project.implementation_type }}</td>
                        </tr>
                        <tr class="datos-implementacion-item">
                            <td
                                class="datos-implementacion-item-title">GDL:</td>
                            <td
                                class="datos-implementacion-item-value">{{ project.golive_initial_date|date:"d/m/y" }}
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="implementacion-section">
                    <h2 class="title-section">
                        EQUIPO ROSCLAR
                    </h2>
                    <table class="datos-implementacion-table">
                        <tr class="datos-implementacion-item">
                            <td
                                class="datos-implementacion-item-title w-extra">Implementador
                                1:</td>
                            <td
                                class="datos-implementacion-item-value w-extra">{{ project.implementer1.name }}</td>
                            <td
                                class="datos-implementacion-item-value">{{ project.implementer1.email }}</td>
                        </tr>
                        <tr class="datos-implementacion-item">
                            <td
                                class="datos-implementacion-item-title">Implementador
                                2:</td>
                            <td class="datos-implementacion-item-value">{{ project.implementer2.name }}</td>
                            <td
                                class="datos-implementacion-item-value">{{ project.implementer2.email }}</td>
                        </tr>
                        <tr class="datos-implementacion-item">
                            <td
                                class="datos-implementacion-item-title">Backup:</td>
                            <td class="datos-implementacion-item-value">{{ project.backup.name }}</td>
                            <td
                                class="datos-implementacion-item-value">{{ project.backup.email }}</td>
                        </tr>
                        <tr class="datos-implementacion-item">
                            <td
                                class="datos-implementacion-item-title">Coordinador:</td>
                            <td class="datos-implementacion-item-value">{{ project.coordinator.name }}</td>
                            <td
                                class="datos-implementacion-item-value">{{ project.coordinator.email }}</td>
                        </tr>
                    </table>
                </div>
            </section>

            <section class="avance">
                <h2 class="title-section">
                    AVANCE DEL PROYECTO
                </h2>
                <table class="avance-table">
                    <thead>
                        <tr>
                            <th class="avance-table-header">FASE</th>
                            <th class="avance-table-header">PREVISTO<br />F.
                                Inicio - F. Fin</th>
                            <th class="avance-table-header">REAL<br />F. Inicio
                                - F. Fin</th>
                            <th class="avance-table-header">AVANCE<br />% -
                                Hitos</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="avance-table-row start-row">
                            <td class="start-title-col">START</td>
                            <td class="start-cel">
                                <span class="date-left">{{ project.start_initial_date|date:"d/m/y" }}</span>
                                <span class="date-separator">-</span>
                                <span class="date-right">{{ project.start_final_date|date:"d/m/y" }}</span>
                            </td>
                            <td class="start-cel">
                                <span class="date-left">{% if project.start_real_initial_date %}{{ project.start_real_initial_date|date:"d/m/y" }}{% else %}&nbsp;{% endif %}</span>
                                <span class="date-separator">-</span>
                                <span class="date-right">{% if project.start_real_final_date %}{{ project.start_real_final_date|date:"d/m/y" }}{% else %}&nbsp;{% endif %}</span>
                            </td>
                            <td class="start-cel">
                                <span class="percent-left">{{ percentages.0.percentage }}%</span>
                                <span class="percent-separator">-</span>
                                <span class="percent-right">{{ percentages.0.count }}</span>
                            </td>
                        </tr>
                        <tr class="avance-table-row start-row">
                            <td class="migration-title-col">COLLECTION</td>
                            <td class="migration-cel">
                                <span class="date-left">{{ project.collection_initial_date|date:"d/m/y" }}</span>
                                <span class="date-separator">-</span>
                                <span class="date-right">{{ project.collection_final_date|date:"d/m/y" }}</span>
                            </td>
                            <td class="migration-cel">
                                <span class="date-left">{% if project.collection_real_initial_date %}{{ project.collection_real_initial_date|date:"d/m/y" }}{% else %}&nbsp;{% endif %}</span>
                                <span class="date-separator">-</span>
                                <span class="date-right">{% if project.collection_real_final_date %}{{ project.collection_real_final_date|date:"d/m/y" }}{% else %}&nbsp;{% endif %}</span>
                            </td>
                            <td class="migration-cel">
                                <span class="percent-left">{{ percentages.1.percentage }}%</span>
                                <span class="percent-separator">-</span>
                                <span class="percent-right">{{ percentages.1.count }}</span>
                            </td>
                        </tr>
                        <tr class="avance-table-row collection-row">
                            <td class="collection-title-col">MIGRATION</td>
                            <td class="collection-cel">
                                <span class="date-left">{{ project.migration_initial_date|date:"d/m/y" }}</span>
                                <span class="date-separator">-</span>
                                <span class="date-right">{{ project.migration_final_date|date:"d/m/y" }}</span>
                            </td>
                            <td class="collection-cel">
                                <span class="date-left">{% if project.migration_real_initial_date %}{{ project.migration_real_initial_date|date:"d/m/y" }}{% else %}&nbsp;{% endif %}</span>
                                <span class="date-separator">-</span>
                                <span class="date-right">{% if project.migration_real_final_date %}{{ project.migration_real_final_date|date:"d/m/y" }}{% else %}&nbsp;{% endif %}</span>
                            </td>
                            <td class="collection-cel">
                                <span class="percent-left">{{ percentages.2.percentage }}%</span>
                                <span class="percent-separator">-</span>
                                <span class="percent-right">{{ percentages.2.count }}</span>
                            </td>
                        </tr>
                        <tr class="avance-table-row test-row">
                            <td class="test-title-col">TEST*</td>
                            <td class="test-cel">
                                <span class="date-left">{{ project.test_initial_date|date:"d/m/y" }}</span>
                                <span class="date-separator">-</span>
                                <span class="date-right">{{ project.test_final_date|date:"d/m/y" }}</span>
                            </td>
                            <td class="test-cel">
                                <span class="date-left">{% if project.test_real_initial_date %}{{ project.test_real_initial_date|date:"d/m/y" }}{% else %}&nbsp;{% endif %}</span>
                                <span class="date-separator">-</span>
                                <span class="date-right">{% if project.test_real_final_date %}{{ project.test_real_final_date|date:"d/m/y" }}{% else %}&nbsp;{% endif %}</span>
                            </td>
                            <td class="test-cel">
                                <span class="percent-left">{{ percentages.3.percentage }}%</span>
                                <span class="percent-separator">-</span>
                                <span class="percent-right">{{ percentages.3.count }}</span>
                            </td>
                        </tr>
                        <tr class="avance-table-row golive-row">
                            <td class="golive-title-col">GO LIVE</td>
                            <td class="golive-cel">
                                <span class="date-left">{{ project.golive_initial_date|date:"d/m/y" }}</span>
                                <span class="date-separator">-</span>
                                <span class="date-right">{{ project.golive_final_date|date:"d/m/y" }}</span>
                            </td>
                            <td class="golive-cel">
                                <span class="date-left">{% if project.golive_real_initial_date %}{{ project.golive_real_initial_date|date:"d/m/y" }}{% else %}&nbsp;{% endif %}</span>
                                <span class="date-separator">-</span>
                                <span class="date-right">{% if project.golive_real_final_date %}{{ project.golive_real_final_date|date:"d/m/y" }}{% else %}&nbsp;{% endif %}</span>
                            </td>
                            <td class="golive-cel">
                                <span class="percent-left">{{ percentages.4.percentage }}%</span>
                                <span class="percent-separator">-</span>
                                <span class="percent-right">{{ percentages.4.count }}</span>
                            </td>
                        </tr>
                        <tr class="avance-table-row takeoff-row">
                            <td class="takeoff-title-col">TAKE OFF</td>
                            <td class="takeoff-cel">
                                <span class="date-left">{{ project.incubadora_initial_date|date:"d/m/y" }}</span>
                                <span class="date-separator">-</span>
                                <span class="date-right">{{ project.incubadora_final_date|date:"d/m/y" }}</span>
                            </td>
                            <td class="takeoff-cel">
                                <span class="date-left">{% if project.incubadora_real_initial_date %}{{ project.incubadora_real_initial_date|date:"d/m/y" }}{% else %}&nbsp;{% endif %}</span>
                                <span class="date-separator">-</span>
                                <span class="date-right">{% if project.incubadora_real_final_date %}{{ project.incubadora_real_final_date|date:"d/m/y" }}{% else %}&nbsp;{% endif %}</span>
                            </td>
                            <td class="takeoff-cel">
                                <span class="percent-left">{{ percentages.5.percentage }}%</span>
                                <span class="percent-separator">-</span>
                                <span class="percent-right">{{ percentages.5.count }}</span>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <p class="note">*Para este proyecto se ha acordado realizar {% if project.month1_test and project.month2_test %}los siguientes tests: {{ project.month1_test|date:"m/Y" }} y {{ project.month2_test|date:"m/Y" }}
                            {% elif project.month1_test %}el siguiente test: {{ project.month1_test|date:"m/Y" }}{% elif project.month2_test %}el siguiente test: {{ project.month2_test|date:"m/Y" }}{% else %}tests{% endif %}</p>
            </section>

            <section class="proceso">
                <h2 class="title-section">
                    PROCESO
                </h2>
                <div class="grid-container">
                    <div class="grid-item grid-start">
                        <h3>START</h3>
                        <p>Bienvenida y presentación de ROSCLAR, definición del
                            alcance del proyecto e identificación de los
                            parámetros laborales y fiscales y sus criterios de
                            nómina.</p>
                    </div>
                    <div class="grid-item grid-collection">
                        <h3>MIGRATION</h3>
                        <p>Importación de los datos recopilados de la empresa y
                            de los trabajadores, parametrización en el sistema
                            de la estructura salarial y análisis y subida de
                            históricos de nómina.</p>
                    </div>
                    <div class="grid-item grid-golive">
                        <h3>GO LIVE</h3>

                        <p>Activación oficial del proceso de nómina tras obtener
                            las autorizaciones correspondientes, validar los
                            calendarios y determinar los servicios
                            requeridos.</p>
                    </div>
                    <div class="grid-item grid-migration">
                        <h3>COLLECTION</h3>

                        <p>Entrega por parte del cliente de toda la
                            documentación y los datos solicitados. Recopilación,
                            revisión y validación de la información requerida
                            por parte de ROSCLAR.</p>
                    </div>

                    <div class="grid-item grid-test">
                        <h3>TEST</h3>

                        <p>Comprobación de la correcta configuración del sistema
                            mediante una prueba de pago (Penny Test) y un
                            cálculo de nómina de forma paralela al actual
                            proveedor. Revisión y cierre final de los criterios
                            de nómina.</p>
                    </div>

                    <div class="grid-item grid-takeoff">
                        <h3>TAKE OFF</h3>

                        <p>Cierre completo del proyecto y presentación del
                            equipo de producción asignado.</p>
                    </div>
                </div>
            </section>

            <section class="footer">
                <div class="footer-content">
                    <span>Muntaner, 239 ático</span><br>
                    <span>08021 Barcelona (Spain)</span><br>
                    <span>tel. (+34) 932 173 444</span><br>
                    <span class="boldy"><EMAIL></span><br>
                    <span class="boldy">www.rosclar.com</span>
                </div>
                <div class="footer-logo">
                    <img src="{{ favicon_path }}" alt="ROSCLAR Logo"
                        class="footer-logo-img" />
                </div>
            </section>

            <div class="footer-decorator">
                <div class="footer-box"></div>
            </div>
        </div>
    </body>
</html>