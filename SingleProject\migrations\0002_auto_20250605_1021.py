# Generated by Django 3.2.25 on 2025-06-05 08:21

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('Fields', '0002_remove_field_status'),
        ('Projects', '0002_projectdatechangelog'),
        ('SingleProject', '0001_initial'),
    ]

    operations = [
        migrations.RenameField(
            model_name='fieldsbyproject',
            old_name='field_id',
            new_name='field',
        ),
        migrations.RenameField(
            model_name='fieldsbyproject',
            old_name='phase_id',
            new_name='phase',
        ),
        migrations.RenameField(
            model_name='fieldsbyproject',
            old_name='project_id',
            new_name='project',
        ),
        migrations.AlterUniqueTogether(
            name='fieldsbyproject',
            unique_together={('project', 'phase', 'field')},
        ),
    ]
