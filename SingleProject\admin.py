from django.contrib import admin
from .models import UsersByProject, FieldsByProject, Comment, PhasesByProject

# Register your models here.


@admin.register(UsersByProject)
class UsersByProjectAdmin(admin.ModelAdmin):
    list_display = ('project', 'user', 'role', 'is_active', 'created_at')
    list_filter = ('role', 'is_active', 'created_at')
    search_fields = ('project__alias', 'project__company_name', 'user__name', 'role__type_of_user')
    ordering = ('-created_at',)
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        ('Información Principal', {
            'fields': ('project', 'user', 'role')
        }),
        ('Estado', {
            'fields': ('is_active',)
        }),
        ('Fechas', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )


@admin.register(FieldsByProject)
class FieldsByProjectAdmin(admin.ModelAdmin):
    list_display = ('project', 'field', 'phase', 'status', 'active')
    list_filter = ('status', 'active', 'phase')
    search_fields = ('project__alias', 'field__name', 'phase__name')


@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    list_display = ('user', 'text', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('user__name', 'text')


@admin.register(PhasesByProject)
class PhasesByProjectAdmin(admin.ModelAdmin):
    list_display = ('project', 'phase', 'verified', 'completed')
    list_filter = ('verified', 'completed', 'phase')
    search_fields = ('project__alias', 'phase__name')
