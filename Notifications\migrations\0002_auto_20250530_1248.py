# Generated by Django 3.2.25 on 2025-05-30 10:48

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('Emails', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Notifications', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='notification',
            name='email_template',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='Emails.emailtemplate'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='notification',
            name='recipients',
            field=models.ManyToManyField(related_name='notifications', to=settings.AUTH_USER_MODEL),
        ),
    ]
