import base64
import os

def get_image_as_base64(image_path):
    """Convert an image to Base64 encoding"""
    with open(image_path, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode('utf-8')
    
    # Get the file extension
    _, file_extension = os.path.splitext(image_path)
    file_extension = file_extension.replace('.', '').lower()
    
    # Map extension to MIME type
    mime_types = {
        'png': 'image/png',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'gif': 'image/gif',
        'webp': 'image/webp',
        'svg': 'image/svg+xml'
    }
    
    mime_type = mime_types.get(file_extension, 'image/png')
    
    return f"data:{mime_type};base64,{encoded_string}"

def read_css_file(css_path):
    """Read a CSS file and return its contents"""
    try:
        with open(css_path, 'r', encoding='utf-8') as f:
            return f.read()
    except Exception as e:
        print(f"Error reading CSS file: {e}")
        return ""
