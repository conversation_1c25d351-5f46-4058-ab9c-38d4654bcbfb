# Generated by Django 3.2.25 on 2025-05-30 08:36

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Fields', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('trigger_condition', models.CharField(choices=[('IS_FILLED', 'Es rellenado'), ('ITS_VALUE_IS', 'Su valor es'), ('CONTAINS', 'Contiene'), ('CHANGES', 'Cambia')], max_length=50)),
                ('value', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ('recipients', models.ManyToManyField(blank=True, related_name='notifications', to=settings.AUTH_USER_MODEL)),
                ('trigger_field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trigger_notifications', to='Fields.field')),
            ],
        ),
    ]
