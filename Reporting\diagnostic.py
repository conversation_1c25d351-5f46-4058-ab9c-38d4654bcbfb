from django.http import HttpResponse
from django.views import View
from django.template.loader import render_to_string
from django.conf import settings
import os
from .utils import get_image_as_base64
import subprocess

class DiagnosticView(View):
    def get(self, request):
        # Check if wkhtmltopdf is installed
        wkhtmltopdf_status = "Not found"
        wkhtmltopdf_path = None
        
        try:
            # Try to find wkhtmltopdf in PATH
            result = subprocess.run(['where', 'wkhtmltopdf'], 
                                  stdout=subprocess.PIPE, 
                                  stderr=subprocess.PIPE, 
                                  text=True,
                                  shell=True)
            
            if result.returncode == 0:
                wkhtmltopdf_path = result.stdout.strip()
                wkhtmltopdf_status = f"Found in PATH: {wkhtmltopdf_path}"
            else:
                # Check if it's installed in the default location
                default_path = r'C:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe'
                if os.path.exists(default_path):
                    wkhtmltopdf_path = default_path
                    wkhtmltopdf_status = f"Found at default location: {default_path}"
        except Exception as e:
            wkhtmltopdf_status = f"Error checking wkhtmltopdf: {str(e)}"
            
        # Check static files
        css_path = os.path.join(settings.BASE_DIR, 'static', 'css', 'styles.css')
        pdf_css_path = os.path.join(settings.BASE_DIR, 'static', 'css', 'pdf_styles.css')
        logo_path = os.path.join(settings.BASE_DIR, 'static', 'img', 'logo.png')
        favicon_path = os.path.join(settings.BASE_DIR, 'static', 'img', 'favicon.webp')
        
        # Test Base64 conversion
        try:
            logo_base64 = get_image_as_base64(logo_path)
            logo_base64_status = "Success"
            # Truncate the string for display
            logo_base64_preview = logo_base64[:50] + "..." if logo_base64 else ""
        except Exception as e:
            logo_base64_status = f"Error: {str(e)}"
            logo_base64_preview = ""
            
        # Prepare diagnostic info
        diagnostic_info = f"""
        <html>
        <head>
            <title>PDF Generation Diagnostics</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1 {{ color: #00b0d6; }}
                .section {{ margin-bottom: 20px; }}
                .status-ok {{ color: green; }}
                .status-error {{ color: red; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <h1>PDF Generation Diagnostic Report</h1>
            
            <div class="section">
                <h2>wkhtmltopdf Status</h2>
                <p><strong>Status:</strong> <span class="{'status-ok' if wkhtmltopdf_path else 'status-error'}">{wkhtmltopdf_status}</span></p>
                <p><strong>Path:</strong> {wkhtmltopdf_path or 'Not found'}</p>
                
                <h3>Installation Instructions</h3>
                <p>If wkhtmltopdf is not found, you need to install it:</p>
                <ol>
                    <li>Download from <a href="https://wkhtmltopdf.org/downloads.html" target="_blank">https://wkhtmltopdf.org/downloads.html</a></li>
                    <li>Install to default location (C:\\Program Files\\wkhtmltopdf)</li>
                    <li>Add the bin directory to your PATH</li>
                </ol>
            </div>
            
            <div class="section">
                <h2>Static Files Status</h2>
                <table>
                    <tr>
                        <th>File</th>
                        <th>Path</th>
                        <th>Exists</th>
                    </tr>
                    <tr>
                        <td>CSS File</td>
                        <td>{css_path}</td>
                        <td class="{'status-ok' if os.path.exists(css_path) else 'status-error'}">{os.path.exists(css_path)}</td>
                    </tr>
                    <tr>
                        <td>PDF CSS File</td>
                        <td>{pdf_css_path}</td>
                        <td class="{'status-ok' if os.path.exists(pdf_css_path) else 'status-error'}">{os.path.exists(pdf_css_path)}</td>
                    </tr>
                    <tr>
                        <td>Logo</td>
                        <td>{logo_path}</td>
                        <td class="{'status-ok' if os.path.exists(logo_path) else 'status-error'}">{os.path.exists(logo_path)}</td>
                    </tr>
                    <tr>
                        <td>Favicon</td>
                        <td>{favicon_path}</td>
                        <td class="{'status-ok' if os.path.exists(favicon_path) else 'status-error'}">{os.path.exists(favicon_path)}</td>
                    </tr>
                </table>
            </div>
            
            <div class="section">
                <h2>Base64 Encoding Test</h2>
                <p><strong>Status:</strong> <span class="{'status-ok' if logo_base64_status == 'Success' else 'status-error'}">{logo_base64_status}</span></p>
                <p><strong>Preview:</strong> {logo_base64_preview}</p>
                <p>Base64 Image Test: <img src="{logo_base64}" alt="Logo Base64 Test" style="max-width: 200px;" /></p>
            </div>
            
            <div class="section">
                <h2>Django Settings</h2>
                <p><strong>BASE_DIR:</strong> {settings.BASE_DIR}</p>
                <p><strong>STATIC_URL:</strong> {settings.STATIC_URL}</p>
                <p><strong>STATIC_ROOT:</strong> {getattr(settings, 'STATIC_ROOT', 'Not set')}</p>
                <p><strong>STATICFILES_DIRS:</strong> {getattr(settings, 'STATICFILES_DIRS', 'Not set')}</p>
            </div>
            
        </body>
        </html>
        """
        
        return HttpResponse(diagnostic_info)
