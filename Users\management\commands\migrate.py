from django.core.management.commands.migrate import Command as MigrateCommand
from django.core.management import call_command

class Command(MigrateCommand):
    def handle(self, *args, **options):
        super().handle(*args, **options)  # Run default migrate command
        
        # Ejecución import_users_core
        self.stdout.write(self.style.SUCCESS("✅ Ejecutando import_users_core..."))
        try:
            call_command('import_users_core')
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error running import_users_core: {e}"))
            raise

        # Ejecución seed_ImplementationTypes
        self.stdout.write(self.style.SUCCESS("✅ Ejecutando seed_ImplementationTypes..."))
        try:
            call_command('seed_ImplementationTypes')
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error running seed_ImplementationTypes: {e}"))
            raise
        
        # Ejecución seed_Phases
        self.stdout.write(self.style.SUCCESS("✅ Ejecutando seed_Phases..."))
        try:
            call_command('seed_Phases')
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error running seed_Phases: {e}"))
            raise