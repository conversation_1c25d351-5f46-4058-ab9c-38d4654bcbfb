from django.urls import path
from .views import ReportListView, ReportFilteredListView, ReportDetailView, ReportCreateView, GenerateReportingPDFView
from .views import ReportUpdateView, ReportUpdateView, ReportDeleteView, ReportToggleView, GenerateProjectPlanPDFView
from .diagnostic import DiagnosticView


urlpatterns = [
    path('list/', ReportListView.as_view(), name='report-list'),
    path('active/', ReportFilteredListView.as_view(), name='report-active-list'),
    path('<int:id>/', ReportDetailView.as_view(), name='report-detail'),
    path('create/', ReportCreateView.as_view(), name='report-create'),
    path('update/<int:id>/', ReportUpdateView.as_view(), name='report-update'),
    path('delete/<int:id>/', ReportDeleteView.as_view(), name='report-delete'),
    path('toggle/<int:id>/', ReportToggleView.as_view(), name='report-toggle'),
    path('project-plan/<int:project_id>/', GenerateProjectPlanPDFView.as_view(), name='generate-project-plan-pdf'),
    path('generate/<int:project_id>/', GenerateReportingPDFView.as_view(), name='generate-reporting-pdf'),
    path('diagnostics/', DiagnosticView.as_view(), name='pdf-diagnostics'),
    
]