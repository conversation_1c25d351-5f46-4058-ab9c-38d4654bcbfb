from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from .models import Phase, Subphase
from Users.models import RMOUser

class SubphaseOrderTests(APITestCase):

    def setUp(self):
        self.user = RMOUser.objects.create_user(password='testpass', email='testemail')
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)

        self.phase = Phase.objects.filter(id=1).first()
        self.sub1 = Subphase(name="Subphase 1", phase=self.phase)
        self.sub1.save()
        self.sub2 = Subphase(name="Subphase 2", phase=self.phase)
        self.sub2.save()
        self.sub3 = Subphase(name="Subphase 3", phase=self.phase)
        self.sub3.save()

    def test_increase_order(self):
        url = reverse('increase-subphase-order', args=[self.sub2.id])
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.sub1.refresh_from_db()
        self.sub2.refresh_from_db()
        self.assertEqual(self.sub2.order, 1)
        self.assertEqual(self.sub1.order, 2)

    def test_increase_order_lowest(self):
        url = reverse('increase-subphase-order', args=[self.sub1.id])
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("lowest order", response.data["error"].lower())

    def test_decrease_order(self):
        url = reverse('decrease-subphase-order', args=[self.sub2.id])
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        self.sub2.refresh_from_db()
        self.sub3.refresh_from_db()
        self.assertEqual(self.sub2.order, 3)
        self.assertEqual(self.sub3.order, 2)

    def test_decrease_order_highest(self):
        url = reverse('decrease-subphase-order', args=[self.sub3.id])
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("highest order", response.data["error"].lower())
