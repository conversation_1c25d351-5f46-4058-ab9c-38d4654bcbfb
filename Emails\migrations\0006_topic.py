# Generated by Django 3.2.25 on 2025-07-23 17:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('Emails', '0005_emailtemplate_is_external'),
    ]

    operations = [
        migrations.CreateModel(
            name='Topic',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
            ],
            options={
                'verbose_name': 'Topic',
                'verbose_name_plural': 'Topics',
                'db_table': 'emails_topics',
            },
        ),
    ]
