from django.core.mail import send_mail
from django.template import loader
from django.utils.html import strip_tags

from RMO.settings import EMAIL_HOST_USER, SEND_MAIL
from .models import NotificationLog

def send_notification(project, notification):
    """
    Envia una notificación por correo electrónico basada en las reglas de notificación del proyecto.
    """

    if SEND_MAIL:

        recipient_emails = [user.email for user in notification.all_recipients.all() if user.is_active]
        subject = project.lid + " - " + project.company_name + " - " + notification.email_template.subject
        
        context = {
            "project": project,
            "notification": notification,
            "project_link": f"https://rmo.com/project/{project.id}/", # TO DO update the link to the actual project link
        }

        template_name = "notification_email.html"
        convert_to_html_content = loader.render_to_string(template_name=template_name, context=context)
        plain_message = strip_tags(convert_to_html_content)

        send_mail(
            subject=subject,
            message=plain_message,
            from_email=EMAIL_HOST_USER,
            recipient_list=recipient_emails,
            html_message=convert_to_html_content,
            fail_silently=False
        )

        # Log the notification
        NotificationLog.objects.create(
            notification=notification,
            project=project,
            subject=subject,
            email=plain_message,
            recipients=", ".join(recipient_emails)
        )
        return 