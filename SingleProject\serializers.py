from rest_framework import serializers
from .models import <PERSON>ByProject, FieldsByProject, Comment, PhasesByProject
from Projects.serializers import ProjectSerializer
from Users.models import RMOUser
from Projects.models import TypeOfUsers


class UsersByProjectSerializer(serializers.ModelSerializer):
    project_alias = serializers.CharField(source='project.alias', read_only=True)
    project_company = serializers.CharField(source='project.company_name', read_only=True)
    user_name = serializers.CharField(source='user.name', read_only=True)
    user_email = serializers.Char<PERSON>ield(source='user.email', read_only=True)
    role_name = serializers.Char<PERSON>ield(source='role.type_of_user', read_only=True)

    class Meta:
        model = UsersByProject
        fields = [
            'id', 'project', 'user', 'role', 'is_active', 
            'created_at', 'updated_at',
            'project_alias', 'project_company', 
            'user_name', 'user_email', 'role_name'
        ]
        read_only_fields = ['created_at', 'updated_at']


class UsersByProjectDetailSerializer(serializers.ModelSerializer):
    """Serializer detallado que incluye información completa de proyecto y usuario"""
    project = ProjectSerializer(read_only=True)
    user = serializers.SerializerMethodField()
    role = serializers.SerializerMethodField()

    class Meta:
        model = UsersByProject
        fields = [
            'id', 'project', 'user', 'role', 'is_active', 
            'created_at', 'updated_at'
        ]

    def get_user(self, obj):
        return {
            'id': obj.user.id,
            'name': obj.user.name,
            'email': obj.user.email,
        }

    def get_role(self, obj):
        return {
            'id': obj.role.id,
            'type_of_user': obj.role.type_of_user,
        }


class ProjectUsersGroupedSerializer(serializers.Serializer):
    """Serializer para agrupar usuarios por proyecto"""
    project_id = serializers.IntegerField()
    project_alias = serializers.CharField()
    project_company = serializers.CharField()
    users = UsersByProjectSerializer(many=True)
    
    
class UserProjectsGroupedSerializer(serializers.Serializer):
    """Serializer para agrupar proyectos por usuario"""
    user_id = serializers.IntegerField()
    user_name = serializers.CharField()
    user_email = serializers.CharField()
    projects = UsersByProjectSerializer(many=True)
