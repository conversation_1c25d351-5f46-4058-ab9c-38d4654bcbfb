# Generated by Django 3.2.25 on 2025-06-11 08:14

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('Projects', '0005_project_status'),
        ('Fields', '0002_remove_field_status'),
        ('SingleProject', '0003_auto_20250606_1120'),
    ]

    operations = [
        migrations.AddField(
            model_name='fieldsbyproject',
            name='phase',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, related_name='fields_by_phase', to='Fields.phase'),
            preserve_default=False,
        ),
        migrations.AlterUniqueTogether(
            name='fieldsbyproject',
            unique_together={('project', 'field', 'phase')},
        ),
    ]
