from pathlib import Path
from datetime import timedelta

import os
import pymysql
pymysql.install_as_MySQLdb()


from dotenv import load_dotenv

from celery.schedules import crontab
# from rest_framework_simplejwt.authentication import default_user_authentication_rule


load_dotenv()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
# AUTH_USER_MODEL = 'Users.User'

ENVIRONMENT = os.getenv('ENVIRONMENT')
BBDD_USER =  os.getenv('BBDD_USER')
BBDD_PASSWORD =  os.getenv('BBDD_PASSWORD')
SECRET_KEY_DOTENV = os.getenv('SECRET_KEY')

# EMAIL GRAPH SECRETS
TENANT_ID = os.getenv('TENANT_ID')
CLIENT_ID = os.getenv('CLIENT_ID')
CLIENT_SECRET = os.getenv('CLIENT_SECRET')




# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = SECRET_KEY_DOTENV

# SECURITY WARNING: don't run with debug turned on in production!
if ENVIRONMENT == "PRO":
    DEBUG = False
else:
    DEBUG = True    



ALLOWED_HOSTS = ["*"]

# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.contenttypes',
    'corsheaders', # CORS
    'rest_framework',
    'rest_framework_simplejwt.token_blacklist',#google auth
    'drf_yasg',
    'django_filters',
    "graphene_django",
    "graphql_jwt",
    'Users', 
    'Projects', 
    'Team', 
    'Auth',
    'Fields',
    'Notifications',
    'Rules',
    'Emails',
    'Templates', 
    'SingleProject',
    'Contacts',
    'Reporting',
]


MIDDLEWARE = [
    'corsheaders.middleware.CorsMiddleware', # CORS
    'django.middleware.common.CommonMiddleware', # CORS
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

SWAGGER_SETTINGS = {
    'SECURITY_DEFINITIONS': {
        'Bearer': {
            'type': 'apiKey',
            'name': 'Authorization',
            'in': 'header',
            'description': 'JWT Authorization header using the Bearer scheme. Example: "Authorization: Bearer <token>"',
        }
    },
    'USE_SESSION_AUTH': False,
}

ROOT_URLCONF = 'RMO.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],  # Ensure this line is present
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'RMO.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',  # o postgresql si estás usando PostgreSQL
        'NAME': os.getenv('RMO_DB_NAME_INTERNAL'),
        'USER': os.getenv('RMO_DB_USER_INTERNAL'),
        'PASSWORD': os.getenv('RMO_DB_PASSWORD_INTERNAL'),
        'HOST': os.getenv('RMO_DB_HOST_INTERNAL'),
        'PORT': os.getenv('RMO_DB_PORT_INTERNAL', '3306'),  # asegúrate de usar el puerto correcto
    },
    'core': {
        'ENGINE': 'django.db.backends.mysql',  # o mysql según lo que uses
        'NAME': os.getenv('RMO_DB_NAME_EXTERNAL'),
        'USER': os.getenv('RMO_DB_USER_EXTERNAL'),
        'PASSWORD': os.getenv('RMO_DB_PASSWORD_EXTERNAL'),
        'HOST': os.getenv('RMO_DB_HOST_EXTERNAL'),
        'PORT': os.getenv('RMO_DB_PORT_EXTERNAL', '5432'),
    }
}

    
    

AUTH_USER_MODEL = 'Users.RMOUser'

# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

STATIC_URL = 'static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [os.path.join(BASE_DIR, "static")]

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

 
 # ---------------------------- ADDED -----------------------------


VERSION = "v1"


if ENVIRONMENT == "PRO" or ENVIRONMENT == "PRE" or ENVIRONMENT == "BETA":
    REST_FRAMEWORK = {
        'DEFAULT_FILTER_BACKENDS': [
            'django_filters.rest_framework.DjangoFilterBackend',
        ],

        'DEFAULT_PERMISSION_CLASSES': [
            'rest_framework.permissions.AllowAny',  # Open permissions for testing
        ],
        'DEFAULT_AUTHENTICATION_CLASSES': (
            'rest_framework_simplejwt.authentication.JWTAuthentication',
        ),
        # Clases de permisos predeterminadas
        'DEFAULT_PERMISSION_CLASSES': (
            'rest_framework.permissions.IsAuthenticated',  # Requiere autenticación en todos los endpoints # permission_classes = [AllowAny]  # Permitir acceso sin autenticación
        ),
        'DEFAULT_RENDERER_CLASSES': (
        'rest_framework.renderers.JSONRenderer',  # Only JSONRenderer to return JSON responses
        )
    }
else:
    REST_FRAMEWORK = {
        'DEFAULT_FILTER_BACKENDS': [
            'django_filters.rest_framework.DjangoFilterBackend',
        ],
                'DEFAULT_PERMISSION_CLASSES': [
            'rest_framework.permissions.AllowAny',  # Open permissions for testing
        ],
        'DEFAULT_AUTHENTICATION_CLASSES': (
            'rest_framework.authentication.SessionAuthentication',
            # 'rest_framework_simplejwt.authentication.JWTAuthentication',
        ),
        # 'DEFAULT_RENDERER_CLASSES': (
        # 'rest_framework.renderers.JSONRenderer',  # Only JSONRenderer to return JSON responses
        # )
    }


AUTHENTICATION_BACKENDS = [
    # 'graphql_jwt.backends.JSONWebTokenBackend',
    'django.contrib.auth.backends.ModelBackend',
]

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=12),   # Access token valid for 12 hours
    'REFRESH_TOKEN_LIFETIME': timedelta(days=12),
    'ROTATE_REFRESH_TOKENS': False,
    'BLACKLIST_AFTER_ROTATION': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,
    'AUTH_HEADER_TYPES': ('Bearer',),
    'USER_AUTHENTICATION_RULE': 'rest_framework_simplejwt.authentication.default_user_authentication_rule',

}


# AUTH_USER_MODEL = 'Users.User'

if ENVIRONMENT == "PRO" or ENVIRONMENT == "PRE":
   # En producción deberías permitir el dominio de tu frontend en lugar de 'localhost:3000'
    CORS_ALLOWED_ORIGINS = [
        'http://localhost:3000',
        'http://localhost:3003',
        'http://localhost:5173',
        'https://analytics.infini.es',
        'https://pre.analytics.infini.es',
        'https://beta-rmo.rosclar.com',
        'https://rmo.rosclar.com',
    ]

    CORS_ALLOW_METHODS = [
        'GET',
        'POST',
        'PUT',
        'DELETE',
        'OPTIONS',
        'PATCH',
    ]

    CORS_ALLOW_HEADERS = [
        'Authorization',
        'Content-Type',
        'X-CSRFToken',
        'X-Static-Key'
    ]


if ENVIRONMENT == "BETA":
    # CORS 
    CORS_ALLOWED_ORIGINS = [
        'http://localhost:3000',
        'http://localhost:3003',
    ]

    # Disable CSRF protection in BETA environment
    CSRF_TRUSTED_ORIGINS = ['http://localhost:3000', 'http://localhost:3003']
    CSRF_COOKIE_SECURE = False
    CSRF_COOKIE_HTTPONLY = False
    CSRF_USE_SESSIONS = False
    CSRF_COOKIE_NAME = "csrftoken"
    CSRF_HEADER_NAME = "HTTP_X_CSRFTOKEN"
    CSRF_FAILURE_VIEW = "django.views.csrf.csrf_failure"


# --------------------------------------------- SEE SQL QUERIES -------------------------------------------
#     LOGGING = {
#     'version': 1,
#     'disable_existing_loggers': False,
#     'handlers': {
#         'console': {
#             'class': 'logging.StreamHandler',
#         },
#     },
#     'loggers': {
#         'django.db.backends': {
#             'handlers': ['console'],
#             'level': 'DEBUG',
#             'propagate': False,
#         },
#     },
# }



# --------------------------------------------- MEDIA AND FILE UPLOAD -------------------------------------------
# Add dir for the files uploaded
MEDIA_ROOT = BASE_DIR / 'media'

MEDIA_URL = 'media/'

FILE_UPLOAD_MAX_MEMORY_SIZE = 104857600


# --------------------------------------------- EMAIL -------------------------------------------

# if ENVIRONMENT == "PRO":
#     SEND_MAIL = True
# else:
#     SEND_MAIL = False

SEND_MAIL = True

EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'

EMAIL_HOST = 'outlook.office365.com'  # Microsoft's SMTP server

EMAIL_PORT = 587

EMAIL_USE_TLS = True

EMAIL_HOST_USER = '<EMAIL>'  # Microsoft 365 email

EMAIL_HOST_PASSWORD = 'HaXkjnw38yQcXoBNY0Tf'  #  Microsoft 365 email password

DEFAULT_FROM_EMAIL = EMAIL_HOST_USER
 
# --------------------------------------------- GRAPHQL -------------------------------------------

GRAPHENE = {
    "SCHEMA": "RMO.graphene.schema",  # path to your GraphQL schema
}

# DEFAULT_PERMISSION_CLASSES = (
#     'rest_framework.permissions.IsAuthenticated',  # This will apply to all GraphQL queries and mutations
# )

GRAPHQL_JWT = {
    'JWT_VERIFY_EXPIRATION': True,
    'JWT_EXPIRATION_DELTA': timedelta(days=12),  # Match ACCESS_TOKEN_LIFETIME
    'JWT_AUTH_HEADER_PREFIX': 'Bearer',  # Match AUTH_HEADER_TYPES
    'JWT_SECRET_KEY': SECRET_KEY,  # Match SIGNING_KEY
    'JWT_ALGORITHM': 'HS256',  # Match ALGORITHM
}