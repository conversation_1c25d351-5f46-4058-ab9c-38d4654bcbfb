from rest_framework import routers
from django.urls import path, include
from django.contrib import admin
from django.http import JsonResponse
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)
from django.http import HttpResponse
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
from django.views.decorators.csrf import csrf_exempt

from graphene_django.views import GraphQLView

from Users.views import CustomTokenObtainPairView

router = routers.DefaultRouter()
# router.register(r'proyectos', ProjectViewSet)

def root(request):
    return JsonResponse({"message": "API RMO is running 🚀"})

schema_view = get_schema_view(
    openapi.Info(
        title="RMO API",
        default_version='v1',
        description="Documentación de la API del sistema RMO",
    ),
    public=True,
    permission_classes=(permissions.AllowAny,),
)

def home(request):
    return HttpResponse("""
        <!DOCTYPE html>
        <html lang="es">
        <head>
            <meta charset="UTF-8" />
            <meta name="viewport" content="width=device-width, initial-scale=1" />
            <title>Backend RMO</title>
            <style>
                body {
                    background-color: #f7f9fc;
                    color: #333;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    height: 100vh;
                    margin: 0;
                }
                h1 {
                    color: #2c3e50;
                    font-size: 2.5rem;
                    margin-bottom: 0.5rem;
                }
                p {
                    font-size: 1.2rem;
                    color: #555;
                }
                .links {
                    margin-top: 20px;
                }
                .links a {
                    text-decoration: none;
                    color: #3498db;
                    margin: 0 10px;
                    font-weight: bold;
                }
                .links a:hover {
                    text-decoration: underline;
                }
            </style>
        </head>
        <body>
            <h1>Backend RMO en funcionamiento</h1>
            <p>Bienvenido a la API del proyecto RMO.</p>
            <div class="links">
                <a href="/admin/">Admin</a>
                <a href="/swagger/">Swagger</a>
                <a href="/graphql/">GraphQL</a>
            </div>
        </body>
        </html>
    """)

urlpatterns = [
    path('', home),
    path('api/users/', include('Users.urls')),
    path('api/projects/', include('Projects.urls')),
    path('api/auth/', include('Auth.urls')),
    path('api/fields/', include('Fields.urls')),
    path('api/teams/', include('Team.urls')),
    path('api/templates/', include('Templates.urls')),
    path('api/reporting/', include('Reporting.urls')),
    path('api/emails/', include('Emails.urls')),
    path('api/token/', CustomTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    path('redoc/', schema_view.with_ui('redoc', cache_timeout=0), name='schema-redoc'),
    path('admin/', admin.site.urls),
    path("graphql/", csrf_exempt(GraphQLView.as_view(graphiql=True))),
    path('api/contacts/', include('Contacts.urls')),
    path('api/project/', include('SingleProject.urls')),
    path('api/notifications/', include('Notifications.urls')),
]
