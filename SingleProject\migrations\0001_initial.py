# Generated by Django 3.2.25 on 2025-06-05 08:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Fields', '0002_remove_field_status'),
        ('Projects', '0002_projectdatechangelog'),
    ]

    operations = [
        migrations.CreateModel(
            name='FieldsByProject',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.J<PERSON><PERSON>ield(blank=True, null=True)),
                ('observations', models.TextField(blank=True, null=True)),
                ('active', models.BooleanField(default=True)),
                ('status', models.CharField(choices=[('NOT_STARTED', 'Not Started'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed')], default='NOT_STARTED', max_length=50)),
                ('field_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fields_by_field', to='Fields.field')),
                ('phase_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fields_by_phase', to='Fields.phase')),
                ('project_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fields_by_project', to='Projects.project')),
            ],
            options={
                'unique_together': {('project_id', 'phase_id', 'field_id')},
            },
        ),
    ]
