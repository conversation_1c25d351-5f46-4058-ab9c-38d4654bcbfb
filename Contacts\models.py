from django.db import models
from Projects.models import Project

class Contact(models.Model):
    name = models.Char<PERSON>ield(max_length=255)
    email = models.EmailField(unique=True)
    type = models.CharField(max_length=255)
    position = models.CharField(max_length=255, verbose_name="Cargo en la empresa")

    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='contacts', null=True, blank=True)    
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name
