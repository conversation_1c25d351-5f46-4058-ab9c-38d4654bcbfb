# serializers.py
from rest_framework import serializers
from .models import Phase, Subphase, Field

# Serializer for the Phase model
class PhaseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Phase
        fields = '__all__'


# Serializer for the Subphase model, includes nested Phase and phase_id for write
class SubphaseSerializer(serializers.ModelSerializer):
    phase = PhaseSerializer(read_only=True)
    phase_id = serializers.PrimaryKeyRelatedField(
        queryset=Phase.objects.all(), source='phase', write_only=True
    )

    class Meta:
        model = Subphase
        fields = '__all__'


# Serializer for the Field model, includes nested Subphase and subphase_id for write
class FieldSerializer(serializers.ModelSerializer):
    subphase = SubphaseSerializer(read_only=True)
    subphase_id = serializers.PrimaryKeyRelatedField(
        queryset=Subphase.objects.all(), source='subphase', write_only=True
    )

    class Meta:
        model = Field
        fields = '__all__'
