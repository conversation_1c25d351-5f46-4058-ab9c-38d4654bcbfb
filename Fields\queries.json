{"Query allFields": {"description": "Retrieve all fields", "query": "query { allFields { id name nameEn description type status subphase { id name } weight isMilestone selectionOptions subtasks } }"}, "Query Field": {"description": "Retrieve a specific field", "query": "query { field(id: 1) { id name nameEn description type status subphase { id name } weight isMilestone selectionOptions subtasks } }"}, "Mutation createField": {"description": "Create a new field", "query": "mutation MyMutation { createField(input: { name: 'New Field', nameEn: 'New Field EN', description: 'Description of the new field', type: 'text', status: 'active', subphaseId: 1, weight: 10, isMilestone: false, selectionOptions: [], subtasks: [] }) { field { id name } } }"}, "Mutation updateField": {"description": "Update an existing field", "query": "mutation { updateField(id: 1, input: { name: 'Updated Field', description: 'Updated description' }) { field { id name description } } }"}, "Mutation deleteField": {"description": "Delete a field", "query": "mutation { deleteField(id: 1) { success } }"}, "Query allSubphases": {"description": "Retrieve all subphases", "query": "query { allSubphases { id name phase { id name } order } }"}, "Query Subphase": {"description": "Retrieve a specific subphase", "query": "query { subphase(id: 1) { id name phase { id name } order } }"}, "Mutation createSubphase": {"description": "Create a new subphase", "query": "mutation MyMutation { createSubphase(input: { name: 'New Subphase', phaseId: 1 }) { subphase { id name } } }"}, "Mutation updateSubphase": {"description": "Update an existing subphase", "query": "mutation { updateSubphase(id: 1, input: { name: 'Updated Subphase', phaseId: 1 }) { subphase { id name } } }"}, "Mutation deleteSubphase": {"description": "Delete a subphase", "query": "mutation { deleteSubphase(id: 1) { success } }"}, "Query allPhases": {"description": "Retrieve all phases", "query": "query { allPhases { id name order } }"}, "Query Phase": {"description": "Retrieve a specific phase", "query": "query { phase(id: 1) { id name order } }"}}