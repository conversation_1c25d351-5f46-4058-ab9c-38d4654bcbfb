import jwt
import graphene
from graphene_django.types import DjangoObjectType
from graphql_jwt.decorators import permission_required

from .models import Project
from Users.models import RMOUser
from Team.models import Team
from RMO import settings
from RMO.decorators import custom_login_required
from Templates.models import Template
from SingleProject.models import FieldsByProject, PhasesByProject
from Fields.models import Phase
from SingleProject.utilFunctions import compute_phase_percentage, compute_subphase_percentage, init_rules

# ============================== CLASSES DECLARATIONS ============================== #

class UserType(DjangoObjectType):
    class Meta:
        model = RMOUser
        fields = "__all__"

class TeamType(DjangoObjectType):
    class Meta:
        model = Team
        fields = "__all__"

class TemplateType(DjangoObjectType):
    class Meta:
        model = Template
        fields = "__all__"

class ProjectType(DjangoObjectType):
    class Meta:
        model = Project
        fields = "__all__"
    
    implementer = graphene.Field(UserType)  # Define nested fields explicitly
    backup = graphene.Field(UserType)
    coordinator = graphene.Field(UserType)
    team = graphene.Field(TeamType)
    incubator = graphene.Field(UserType)
    template = graphene.Field(TemplateType)
    actual_phase = graphene.String()
    percentages = graphene.List(graphene.JSONString)

    def resolve_actual_phase(self, info):
        # Get all phases for this project, ordered by phase.order
        all_phases = PhasesByProject.objects.filter(project=self).order_by('phase__order')
        # Find the first phase that is not completed
        first_uncompleted = all_phases.filter(completed=False).first()
        if first_uncompleted:
            return first_uncompleted.phase.name
        # If all are completed, return the last phase's name (or None if no phases)
        last_phase = all_phases.last()
        return last_phase.phase.name if last_phase else None
        
    def resolve_implementer(self, info):
        # Map implementer to implementer1 for backward compatibility
        return self.implementer1
        
    def resolve_percentages(self, info):
        phases = PhasesByProject.objects.filter(project=self)
        percentages = []
        for phase in phases:
            phase_percentage = compute_phase_percentage(self, phase.phase)
            percentages.append({
                'id': phase.phase.id,
                'name': phase.phase.name,
                'percentage': phase_percentage
            })
        return percentages

class ProjectInput(graphene.InputObjectType):
    lid = graphene.String()
    alias = graphene.String()
    company_name = graphene.String()
    aggregator = graphene.String()
    implementation_type = graphene.String()
    implementer1_id = graphene.Int()
    implementer2_id = graphene.Int()
    backup_id = graphene.Int()
    coordinator_id = graphene.Int()
    team_id = graphene.Int()
    incubator_id = graphene.Int()
    template_id = graphene.Int()
    start_initial_date = graphene.Date()
    start_final_date = graphene.Date()
    collection_initial_date = graphene.Date()
    collection_final_date = graphene.Date()
    migration_initial_date = graphene.Date()
    migration_final_date = graphene.Date()
    test_initial_date = graphene.Date()
    test_final_date = graphene.Date()
    month1_test = graphene.Date()
    month2_test = graphene.Date()
    golive_initial_date = graphene.Date()
    golive_final_date = graphene.Date()
    incubadora_initial_date = graphene.Date()
    incubadora_final_date = graphene.Date()
    status = graphene.String()


# ============================== QUERIES DECLARATIONS ============================== #

class Query(graphene.ObjectType):
    all_projects = graphene.List(ProjectType)
    project = graphene.Field(ProjectType, id=graphene.Int(required=True))

    @custom_login_required
    @permission_required('Users.visualizar_proyectos')
    def resolve_all_projects(root, info):
        return Project.objects.all()    
    
    @custom_login_required
    @permission_required('Users.visualizar_proyectos')
    def resolve_project(root, info, id):
        return Project.objects.get(id=id)

# ============================== MUTATIONS DECLARATIONS ============================== #

# Mutation for creating a project
class CreateProjectMutation(graphene.Mutation):
    class Arguments:
        input = ProjectInput(required=True)

    project = graphene.Field(ProjectType)

    @custom_login_required
    @permission_required('Users.editar_proyectos')
    def mutate(self, info, input):
        data = input
        try:
            implementer1 = RMOUser.objects.get(id=data.implementer1_id) if data.implementer1_id else None
        except RMOUser.DoesNotExist:
            raise Exception("El implementador 1 especificado no existe.")
        try:
            implementer2 = RMOUser.objects.get(id=data.implementer2_id) if data.implementer2_id else None
        except RMOUser.DoesNotExist:
            raise Exception("El implementador 2 especificado no existe.")
        try:
            backup = RMOUser.objects.get(id=data.backup_id) if data.backup_id else None
        except RMOUser.DoesNotExist:
            raise Exception("El backup especificado no existe.")
        try:
            coordinator = RMOUser.objects.get(id=data.coordinator_id) if data.coordinator_id else None
        except RMOUser.DoesNotExist:
            raise Exception("El coordinador especificado no existe.")
        try:
            team = Team.objects.get(id=data.team_id) if data.team_id else None
        except Team.DoesNotExist:
            raise Exception("El equipo especificado no existe.")
        try:
            incubator = RMOUser.objects.get(id=data.incubator_id) if data.incubator_id else None
        except RMOUser.DoesNotExist:
            raise Exception("El incubador especificado no existe.")
        try:
            template = Template.objects.get(id=data.template_id) if getattr(data, "template_id", None) else None
        except Template.DoesNotExist:
            raise Exception("La plantilla especificada no existe.")
        project = Project(
            lid=data.lid,
            alias=data.alias,
            company_name=data.company_name,
            aggregator=data.aggregator,
            implementation_type=data.implementation_type.upper(),
            implementer1=implementer1,
            implementer2=implementer2,
            backup=backup,
            coordinator=coordinator,
            team=team,
            incubator=incubator,
            template=template,
            start_initial_date=data.start_initial_date,
            start_final_date=data.start_final_date,
            collection_initial_date=data.collection_initial_date,
            collection_final_date=data.collection_final_date,
            migration_initial_date=data.migration_initial_date,
            migration_final_date=data.migration_final_date,
            test_initial_date=data.test_initial_date,
            test_final_date=data.test_final_date,
            month1_test=data.month1_test,
            month2_test=data.month2_test,
            golive_initial_date=data.golive_initial_date,
            golive_final_date=data.golive_final_date,
            incubadora_initial_date=data.incubadora_initial_date,
            incubadora_final_date=data.incubadora_final_date,
            status=Project.SCHEDULED  # Default status when creating a project
        )
        project.save()

        # Add FieldsByProject rows for all fields in the template
        if template:
            
            for field in template.fields.all():
                FieldsByProject.objects.create(
                    project=project,
                    field=field,
                    phase=field.subphase.phase,
                    value=None,
                    observations=None,
                    active=True,
                    status=FieldsByProject.NOT_STARTED
                )

            for phase in Phase.objects.all():
                PhasesByProject.objects.create(
                    project=project,
                    phase=phase,
                    verified=False,
                    completed=False
                )

        # Init target field of the rules
        init_rules(project)

        return CreateProjectMutation(project=project)

# Mutation for updating a project
class UpdateProjectMutation(graphene.Mutation):
    class Arguments:
        id = graphene.Int(required=True)
        input = ProjectInput(required=True)

    project = graphene.Field(ProjectType)

    @custom_login_required
    @permission_required('Users.editar_proyectos')
    def mutate(self, info, id, input):
        try:
            project = Project.objects.get(id=id)
        except Project.DoesNotExist:
            raise Exception("El proyecto con este ID no existe.")
        data = input
        # Only allow updating specific fields
        allowed_fields = {
            'coordinator_id': RMOUser,
            'backup_id': RMOUser,
            'implementer1_id': RMOUser,
            'implementer2_id': RMOUser,
            'incubator_id': RMOUser,
            'team_id': Team,
            'status': None
        }
        for field, model in allowed_fields.items():
            value = getattr(data, field, None)
            if value is not None:
                if model is not None:
                    try:
                        related_instance = model.objects.get(id=value)
                        setattr(project, field[:-3], related_instance)
                    except model.DoesNotExist:
                        raise Exception(f"El objeto relacionado para el campo {field} no existe.")
                else:
                    setattr(project, field, value)
        
        # Allow template change only if no field is completed
        if getattr(data, 'template_id', None) is not None:
            has_completed = FieldsByProject.objects.filter(project=project, status=FieldsByProject.COMPLETED).exists()
            if has_completed:
                raise Exception("No se puede cambiar la plantilla porque hay campos completados en el proyecto.")
            try:
                template = Template.objects.get(id=data.template_id)
                project.template = template

                # Eliminar todos los campos antiguos de FieldsByProject y añadir los nuevos de la nueva plantilla
                FieldsByProject.objects.filter(project=project).delete()
                for field in template.fields.all():
                    FieldsByProject.objects.create(
                        project=project,
                        field=field,
                        phase=field.subphase.phase,
                        value=None,
                        observations=None,
                        active=True,
                        status=FieldsByProject.NOT_STARTED
                    )

            except Template.DoesNotExist:
                raise Exception("La plantilla especificada no existe.")
        project.save()
        return UpdateProjectMutation(project=project)

# Mutation for deleting a project
class DeleteProjectMutation(graphene.Mutation):
    class Arguments:
        id = graphene.Int()

    success = graphene.Boolean()

    @custom_login_required
    @permission_required('Users.editar_proyectos')
    def mutate(self, info, id):
        try:
            project = Project.objects.get(id=id)
        except Project.DoesNotExist:
            raise Exception("El proyecto con este ID no existe.")
        
        # Delete all FieldsByProject records related to this project
        FieldsByProject.objects.filter(project=project).delete()
        PhasesByProject.objects.filter(project=project).delete()
        project.delete()
        return DeleteProjectMutation(success=True)

# Define the Mutation class
class Mutation(graphene.ObjectType):
    create_project = CreateProjectMutation.Field()
    update_project = UpdateProjectMutation.Field()
    delete_project = DeleteProjectMutation.Field()

# Create schema with both Query and Mutation
schema = graphene.Schema(query=Query, mutation=Mutation)