{"Query allProjects": {"description": "Retrieve all projects", "query": "query { allProjects { id lid alias companyName aggregator implementationType implementer { id email } implementer2 { id email } backup { id email } coordinator { id email } incubator { id email } team { id name } startInitialDate startFinalDate collectionInitialDate collectionFinalDate migrationInitialDate migrationFinalDate testInitialDate testFinalDate month1Test month2Test goliveInitialDate goliveFinalDate incubadoraInitialDate incubadoraFinalDate template } }"}, "Query Project": {"description": "Retrieve a specific project", "query": "query { project(id: 1) { id lid alias companyName aggregator implementationType implementer { id email } implementer2 { id email } backup { id email } coordinator { id email } incubator { id email } team { id name } startInitialDate startFinalDate collectionInitialDate collectionFinalDate migrationInitialDate migrationFinalDate testInitialDate testFinalDate month1Test month2Test goliveInitialDate goliveFinalDate incubadoraInitialDate incubadoraFinalDate template } }"}, "Mutation createProject": {"description": "Create a new project", "query": "mutation MyMutation { createProject(input: { lid: 'LID-001', alias: 'Project Alpha', companyName: 'ExampleCorp', aggregator: 'AggregatorX', implementationType: 'Standard', implementer1Id: 10, implementer2Id: 11, backupId: 12, coordinatorId: 13, teamId: 1, incubatorId: 14, startInitialDate: '2025-06-01', startFinalDate: '2025-06-30', collectionInitialDate: '2025-07-01', collectionFinalDate: '2025-07-15', migrationInitialDate: '2025-08-01', migrationFinalDate: '2025-08-15', testInitialDate: '2025-09-01', testFinalDate: '2025-09-10', month1Test: '2025-10-01', month2Test: '2025-11-01', goliveInitialDate: '2025-12-01', goliveFinalDate: '2025-12-15', incubadoraInitialDate: '2026-01-01', incubadoraFinalDate: '2026-01-31' }) { project { id alias } } }"}, "Mutation updateProject": {"description": "Update an existing project", "query": "mutation { updateProject(id: 1, input: { alias: 'Updated Alias' }) { project { id lid alias companyName } } }"}, "Mutation deleteProject": {"description": "Delete a project", "query": "mutation { deleteProject(id: 1) { success } }"}}