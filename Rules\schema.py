import graphene
from graphene import Enum
from graphene_django.types import DjangoObjectType
from graphql_jwt.decorators import permission_required

from RMO.decorators import custom_login_required
from .models import Rule
from Fields.models import Field
from Users.models import RMOUser
from Fields.schema import FieldType

# ============================== CLASSES DECLARATION ============================== #

class RuleType(DjangoObjectType):
    template_count = graphene.Int()

    class Meta:
        model = Rule
        fields = "__all__"
    
    def resolve_template_count(self, info):
        return self.templates.count()

class RuleInput(graphene.InputObjectType):
    name = graphene.String()
    description = graphene.String()
    rule = graphene.String()
    origin_field_id = graphene.ID(required=True)
    value = graphene.String()
    target_field_id = graphene.ID(required=True)
    condition = graphene.String()
    action = graphene.String()
    status = graphene.String()

# ============================== QUERIES DECLARATIONS ============================== #

class Query(graphene.ObjectType):
    all_rules = graphene.List(RuleType)
    rule = graphene.Field(RuleType, id=graphene.Int(required=True))

    @custom_login_required
    @permission_required('Users.visualizar_config')
    def resolve_all_rules(self, info):
        return Rule.objects.all()

    @custom_login_required
    @permission_required('Users.visualizar_config')
    def resolve_rule(self, info, id):
        return Rule.objects.get(id=id)

# ============================== MUTATIONS DECLARATIONS ============================== #

class CreateRule(graphene.Mutation):
    class Arguments:
        rule_data = RuleInput(required=True)

    rule = graphene.Field(RuleType)

    @custom_login_required
    @permission_required('Users.editar_config')
    def mutate(self, info, rule_data):
        try:
            origin_field = Field.objects.get(id=rule_data.origin_field_id)
        except Field.DoesNotExist:
            raise Exception("El campo de origen especificado no existe.")
        try:
            target_field = Field.objects.get(id=rule_data.target_field_id)
        except Field.DoesNotExist:
            raise Exception("El campo de destino especificado no existe.")

        # TO DO lógica edición dependiendo de si la rule está asociada a una plantilla o no

        # Decideix si cal guardar el 'value' segons la condició
        value = rule_data.value if rule_data.condition in ['CONTENT_EQUAL_TO', 'CONTENT_UNEQUAL_TO'] else None

        rule = Rule(
            name=rule_data.name,
            description=rule_data.description,
            rule=rule_data.rule,
            origin_field=origin_field,
            value = value,
            target_field=target_field,
            condition=rule_data.condition,
            action=rule_data.action,
            status='ACTIVE' if rule_data.status else 'INACTIVE'
        )
        rule.save()
        rule.rule = rule.format_rule_info()
        rule.save()
        return CreateRule(rule=rule)
    

class UpdateRule(graphene.Mutation):
    class Arguments:
        id = graphene.ID(required=True)
        name = graphene.String()
        description = graphene.String()
        status = graphene.String()
        value = graphene.String()
        action = graphene.String()
        rule = graphene.String()
        origin_field_id = graphene.ID()
        target_field_id = graphene.ID()
        condition = graphene.String()

    rule = graphene.Field(RuleType)

    @custom_login_required
    @permission_required('Users.editar_config')
    def mutate(self, info, id, name=None, description=None, status=None, value=None, action=None, rule=None, origin_field_id=None, target_field_id=None, condition=None):
        try:
            rule_obj = Rule.objects.get(id=id)
        except Rule.DoesNotExist:
            raise Exception("La regla con este ID no existe.")

        assigned_to_template = rule_obj.templates.exists()

        # Always editable fields
        if name is not None:
            rule_obj.name = name
        if description is not None:
            rule_obj.description = description
        if status in ['ACTIVE', 'INACTIVE']:
            rule_obj.status = status
        if value is not None:
            rule_obj.value = value
        if action is not None:
            rule_obj.action = action

        # Only editable if not assigned to any template
        if not assigned_to_template:
            if rule is not None:
                rule_obj.rule = rule
            if origin_field_id is not None:
                try:
                    origin_field = Field.objects.get(id=origin_field_id)
                    rule_obj.origin_field = origin_field
                except Field.DoesNotExist:
                    raise Exception("El campo de origen especificado no existe.")
            if target_field_id is not None:
                try:
                    target_field = Field.objects.get(id=target_field_id)
                    rule_obj.target_field = target_field
                except Field.DoesNotExist:
                    raise Exception("El campo de destino especificado no existe.")
            if condition is not None:
                rule_obj.condition = condition

        rule_obj.save()
        return UpdateRule(rule=rule_obj)

class DeleteRule(graphene.Mutation):
    class Arguments:
        id = graphene.ID(required=True)

    ok = graphene.Boolean()

    @custom_login_required
    @permission_required('Users.editar_config')
    def mutate(self, info, id):
        try:
            rule = Rule.objects.get(id=id)
            rule.delete()
            return DeleteRule(ok=True)
        except Rule.DoesNotExist:
            raise Exception("La regla con este ID no existe.")


class Mutation(graphene.ObjectType):
    create_rule = CreateRule.Field()
    update_rule = UpdateRule.Field()
    delete_rule = DeleteRule.Field()