from django.db import models
from Projects.models import Project, TypeOfUsers
from Fields.models import Phase
from Fields.models import Field
from Users.models import RMOUser  # Adjust the import path as needed
# Create your models here.


class UsersByProject(models.Model):
    """
    Tabla que normaliza la relación entre proyectos, usuarios y sus roles.
    Recopila la información de usuarios asignados a cada proyecto según los roles:
    Coordinador, Implementador, Implementador 2, Backup Implementador, Incubadora
    """
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='users_by_project')
    user = models.ForeignKey(RMOUser, on_delete=models.CASCADE, related_name='projects_by_user')
    role = models.ForeignKey(TypeOfUsers, on_delete=models.CASCADE, related_name='users_by_role')
    
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('project', 'user', 'role')
        verbose_name = "Usuario por Proyecto"
        verbose_name_plural = "Usuarios por Proyecto"

    def __str__(self):
        return f"{self.project.alias} - {self.user.name} - {self.role.type_of_user}"


class FieldsByProject(models.Model):
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='fields_by_project')
    field = models.ForeignKey(Field, on_delete=models.CASCADE, related_name='fields_by_field')
    phase = models.ForeignKey(Phase, on_delete=models.CASCADE, related_name='fields_by_phase')

    value = models.JSONField(blank=True, null=True)
    observations = models.TextField(blank=True, null=True)

    active = models.BooleanField(default=True)

    NOT_STARTED = 'NOT_STARTED'
    IN_PROGRESS = 'IN_PROGRESS'
    COMPLETED = 'COMPLETED'
    CANCELLED = 'CANCELLED'

    STATUS_CHOICES = [
        (NOT_STARTED, 'Not Started'),
        (IN_PROGRESS, 'In Progress'),
        (COMPLETED, 'Completed'),
        (CANCELLED, 'Cancelled')
    ]

    status = models.CharField(max_length=50, choices=STATUS_CHOICES, default=NOT_STARTED)

    class Meta:
        unique_together = ('project', 'field', 'phase')

    def __str__(self):
        return f"{self.project.alias} - {self.field.name} - {self.phase.name}"


class Comment(models.Model):
    user = models.ForeignKey(RMOUser, on_delete=models.CASCADE, related_name='comments')
    text = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.field.name} - {self.user.name} - {self.created_at}"


class PhasesByProject(models.Model):
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='phases_by_project')
    phase = models.ForeignKey(Phase, on_delete=models.CASCADE, related_name='phases_by_phase')

    verified = models.BooleanField(default=False)
    completed = models.BooleanField(default=False)
    
    comments = models.ManyToManyField(Comment, blank=True, related_name='phases_by_comments')

    class Meta:
        unique_together = ('project', 'phase')

    def __str__(self):
        return f"{self.project.alias} - {self.phase.name}"