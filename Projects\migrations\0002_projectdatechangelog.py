# Generated by Django 3.2.25 on 2025-06-02 07:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Fields', '0001_initial'),
        ('Projects', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProjectDateChangeLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(default=django.utils.timezone.now)),
                ('field_type', models.CharField(max_length=100)),
                ('new_value', models.DateField(blank=True, null=True)),
                ('comment', models.TextField(blank=True)),
                ('phase', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='Fields.phase')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='date_change_logs', to='Projects.project')),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
    ]
