from django.core.management.base import BaseCommand
from Emails.models import Topic

class Command(BaseCommand):
    help = 'Seeds the Topic table with predefined elements'

    def handle(self, *args, **options):
        # Define the elements to insert
        predefined_topics = [
            {'name': 'Transitions'},
            {'name': '<PERSON><PERSON><PERSON>'},
            {'name': 'Add-on'},
            {'name': 'Go Live'},
            {'name': 'Payment Method'},
            {'name': 'It Services'},
            {'name': 'Trebla Services'},
            {'name': 'Presentación De Impuestos'},
            {'name': 'Externos'}
        ]

        # Insert elements if they don't exist
        for topic_data in predefined_topics:
            if not Topic.objects.filter(name=topic_data['name']).exists():
                Topic.objects.create(**topic_data)

        self.stdout.write(self.style.SUCCESS('✅ Topics añadidos correctamente'))
