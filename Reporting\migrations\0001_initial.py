# Generated by Django 3.2.25 on 2025-06-19 08:32

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Fields', '0003_auto_20250619_1032'),
    ]

    operations = [
        migrations.CreateModel(
            name='Report',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('description', models.Char<PERSON>ield(max_length=255)),
                ('document_name', models.CharField(max_length=255)),
                ('observations', models.BooleanField(default=False)),
                ('use_english', models.BooleanField(default=False)),
                ('filters', models.JSONField(blank=True, default=dict)),
                ('fields', models.ManyToManyField(blank=True, related_name='reports', to='Fields.Field')),
            ],
        ),
    ]
