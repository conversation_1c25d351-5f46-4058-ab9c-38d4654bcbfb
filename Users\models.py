import uuid

from django.db import models
from Team.models import Team
from Users.customUserManager import CustomUserManager

from django.contrib.auth.models import AbstractBaseUser, Permission, BaseUserManager, PermissionsMixin
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth.models import BaseUserManager

class RMOUserManager(BaseUserManager):
    def get_by_natural_key(self, email):
        return self.get(email=email)
    
    def create_user(self, email, password=None, **extra_fields):
        if not email:
            raise ValueError('El email es obligatorio')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser debe tener is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser debe tener is_superuser=True.')

        return self.create_user(email, password, **extra_fields)

class RMOUser(AbstractBaseUser, PermissionsMixin):
    name = models.CharField(max_length=255)
    email = models.EmailField(unique=True)
    password = models.CharField(max_length=255, default="")  # requerido por Django
    team = models.ForeignKey(Team, on_delete=models.SET_NULL, null=True, blank=True)
    position = models.CharField(max_length=255)
    team_code = models.CharField(max_length=255, null=True, blank=True)
    first_name = models.CharField(max_length=150, blank=True, null=True)
    last_name = models.CharField(max_length=150, blank=True, null=True)
    is_staff = models.BooleanField(default=False)
    is_superuser = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    must_change_password = models.BooleanField(default=True)

    objects = RMOUserManager()

    def save(self, *args, **kwargs):
        first = self.first_name or ''
        last = self.last_name or ''
        self.name = f"{first} {last}".strip()
        super().save(*args, **kwargs)

    
    

    USERNAME_FIELD = 'email'

    objects = CustomUserManager()
    REQUIRED_FIELDS = ['first_name', 'last_name']


    def __str__(self):
        return f"{self.name} - {self.email}"
    
    class Meta:
        permissions = (
                        ('visualizar_proyectos', 'Puede visualizar proyectos'),
                        ('editar_proyectos', 'Puede editar proyectos'),
                        ('visualizar_config', 'Puede visualizar la configuración'),
                        ('editar_config', 'Puede editar la configuración'),
                        ('visualizar_usuarios', 'Puede visualizar usuarios'),
                        ('editar_usuarios', 'Puede editar usuarios'),
                        ('verificar_fase', 'Puede verificar fases'),
                        ('editar_campos', 'Puede editar campos de proyecto'),
                        ('visualizar_contactos', 'Puede visualizar contactos'),
                        ('editar_contactos', 'Puede editar contactos'),
                    )

    def generate_reset_password_token(self):
        token = str(uuid.uuid4())
        self.reset_password_token = token
        self.save(update_fields=["reset_password_token"])
        return token

    def clear_reset_password_token(self):
        self.reset_password_token = None
        self.save(update_fields=["reset_password_token"])