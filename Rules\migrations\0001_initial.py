# Generated by Django 3.2.25 on 2025-05-30 08:36

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('Fields', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Rule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=255, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('rule', models.TextField(blank=True, null=True)),
                ('value', models.CharField(blank=True, max_length=255, null=True)),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('INACTIVE', 'Inactive')], default='INACTIVE', max_length=50)),
                ('condition', models.CharField(choices=[('HAS_CONTENT', 'Tiene contenido'), ('CONTENT_EQUAL_TO', 'Contenido igual a'), ('CONTENT_UNEQUAL_TO', 'Contenido diferente de'), ('IS_COMPLETED', 'Está completado'), ('IS_NOT_COMPLETED', 'No está completado'), ('IN_PROGRESS', 'Está en progreso')], max_length=50)),
                ('action', models.CharField(choices=[('ENABLE', 'Habilitar'), ('DISABLE', 'Deshabilitar')], max_length=50)),
                ('origin_field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='origin_field_rules', to='Fields.field')),
                ('target_field', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='target_field_rules', to='Fields.field')),
            ],
        ),
    ]
