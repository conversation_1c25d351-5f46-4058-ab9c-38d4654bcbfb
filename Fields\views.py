from django.shortcuts import render
from rest_framework.views import APIView
from rest_framework.response import Response
from .models import Subphase, Field, Phase
from rest_framework.permissions import IsAuthenticated
from Users.utilFunctions import permissions_required
from rest_framework import status
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.db import models

class SubphaseFieldsSummaryView(APIView):
    """
    API endpoint that returns all subphases with the count and names of their associated fields.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_config')]
    @swagger_auto_schema(
        operation_summary="List subphases with their fields count and names",
        responses={200: openapi.Response(
            description="List of subphases with field counts and names",
            examples={
                "application/json": [
                    {
                        "subphase_name": "Subphase 1",
                        "fields_count": 2,
                        "fields": ["Field A", "Field B"]
                    }
                ]
            }
        )}
    )
    def get(self, request):
        data = []
        for subphase in Subphase.objects.all():
            fields = Field.objects.filter(subphase=subphase)
            data.append({
                "subphase_id": subphase.id,
                "subphase_name": subphase.name,
                "fields_count": fields.count(),
                "fields": [f.name for f in fields]
            })
        return Response(data)

class SubphaseDecreaseOrderView(APIView):
    """
    API endpoint to decrease the order of a subphase by one, swapping with the next subphase in order.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.editar_config')]  
    @swagger_auto_schema(
        operation_summary="Decrease subphase order",
        responses={
            200: openapi.Response(description="Order decreased"),
            400: openapi.Response(description="Subphase already has the highest order or not found"),
            404: openapi.Response(description="Subphase not found")
        }
    )
    def post(self, request, subphase_id):
        try:
            subphase = Subphase.objects.get(id=subphase_id)
        except Subphase.DoesNotExist:
            return Response({"error": "Subfase no encontrada."}, status=status.HTTP_404_NOT_FOUND)

        # Find the next subphase in order
        next_subphase = Subphase.objects.filter(
            phase=subphase.phase,
            order=subphase.order + 1
        ).first()

        if not next_subphase:
            return Response({"error": "La subfase ya tiene el mayor orden."}, status=status.HTTP_400_BAD_REQUEST)

        # Swap orders
        original_order = subphase.order
        subphase.order = next_subphase.order
        next_subphase.order = original_order
        subphase.save()
        next_subphase.save()

        return Response({"success": True, "message": "Orden aumentado."})

class SubphaseIncreaseOrderView(APIView):
    """
    API endpoint to increase the order of a subphase by one, swapping with the previous subphase in order.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.editar_config')]
    @swagger_auto_schema(
        operation_summary="Increase subphase order",
        responses={
            200: openapi.Response(description="Order increased"),
            400: openapi.Response(description="Subphase already has the lowest order or not found"),
            404: openapi.Response(description="Subphase not found")
        }
    )
    def post(self, request, subphase_id):
        try:
            subphase = Subphase.objects.get(id=subphase_id)
        except Subphase.DoesNotExist:
            return Response({"error": "Subfase no encontrada."}, status=status.HTTP_404_NOT_FOUND)

        if subphase.order == 1:
            return Response({"error": "La subfase ya tiene el menor orden."}, status=status.HTTP_400_BAD_REQUEST)

        # Find the previous subphase in order
        prev_subphase = Subphase.objects.filter(
            phase=subphase.phase,
            order=subphase.order - 1
        ).first()

        if not prev_subphase:
            return Response({"error": "No hay una subfase anterior para intercambiar."}, status=status.HTTP_400_BAD_REQUEST)

        # Swap orders
        original_order = subphase.order
        subphase.order = prev_subphase.order
        prev_subphase.order = original_order
        subphase.save()
        prev_subphase.save()

        return Response({"success": True, "message": "Orden disminuido."})

class MoveSubphaseView(APIView):
    """
    Endpoint para mover una subfase a otra fase y actualizar los órdenes correspondientes.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.editar_config')]
    @swagger_auto_schema(
        operation_summary="Mover subfase a otra fase",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["subphase_id", "destination_phase_id"],
            properties={
                "subphase_id": openapi.Schema(type=openapi.TYPE_INTEGER),
                "destination_phase_id": openapi.Schema(type=openapi.TYPE_INTEGER),
            },
        ),
        responses={
            200: openapi.Response(description="Subfase movida correctamente"),
            400: openapi.Response(description="Entrada u operación inválida"),
            404: openapi.Response(description="Subfase o fase no encontrada"),
        }
    )
    def post(self, request):
        subphase_id = request.data.get("subphase_id")
        destination_phase_id = request.data.get("destination_phase_id")
        if not subphase_id or not destination_phase_id:
            return Response({"error": "subphase_id y destination_phase_id son requeridos."}, status=status.HTTP_400_BAD_REQUEST)
        try:
            subphase = Subphase.objects.get(id=subphase_id)
        except Subphase.DoesNotExist:
            return Response({"error": "Subfase no encontrada."}, status=status.HTTP_404_NOT_FOUND)
        # Check if subphase has any fields
        if Field.objects.filter(subphase=subphase).exists():
            return Response({"error": "No se puede mover la subfase porque tiene campos asociados."}, status=status.HTTP_400_BAD_REQUEST)
        try:
            destination_phase = Phase.objects.get(id=destination_phase_id)
        except Phase.DoesNotExist:
            return Response({"error": "Fase destino no encontrada."}, status=status.HTTP_404_NOT_FOUND)
        old_phase = subphase.phase
        if old_phase == destination_phase:
            return Response({"error": "La subfase ya está en la fase destino."}, status=status.HTTP_400_BAD_REQUEST)
        
        # Asignar nueva fase y orden
        max_order = Subphase.objects.filter(phase=destination_phase).aggregate(models.Max('order'))['order__max'] or 0
        subphase.phase = destination_phase
        subphase.order = max_order + 1
        subphase.save()
        # Reindexar órdenes en la fase antigua
        old_subphases = Subphase.objects.filter(phase=old_phase).order_by('order')
        for idx, sp in enumerate(old_subphases, start=1):
            if sp.order != idx:
                sp.order = idx
                sp.save()
        return Response({"success": True, "message": "Subfase movida correctamente."})


