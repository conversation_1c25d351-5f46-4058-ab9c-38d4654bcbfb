from django.db import models
from Fields.models import Field
from Rules.models import Rule
from Notifications.models import Notification

class Template(models.Model):
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)

    fields = models.ManyToManyField(Field, related_name='templates')
    rules = models.ManyToManyField(Rule, related_name='templates')
    notifications = models.ManyToManyField(Notification, related_name='templates')

    full_definition = models.JSONField(blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True, null=True)
    updated_at = models.DateTimeField(auto_now=True, null=True)

    def __str__(self):
        return self.name

    # Métodos útiles:

    def get_fields(self):
        return self.fields.all()

    def get_rules(self):
        return self.rules.all()

    def get_notifications(self):
        return self.notifications.all()
    
    def generate_full_definition(self):
        """
        Generates the full_definition JSON with current fields, rules and notifications.
        """
        self.full_definition = {
            "fields": [
                {
                    "id": field.id,
                    "name": field.name,
                    "description": field.description,
                    "type": field.type,
                    "is_milestone": field.is_milestone,
                    "weight": str(field.weight),
                    "subphase": field.subphase.name
                }
                for field in self.get_fields()
            ],
            "rules": [
                {
                    "id": rule.id,
                    "name": rule.name,
                    "description": rule.description,
                    "origin_field": rule.origin_field.name,
                    "target_field": rule.target_field.name,
                    "condition": rule.condition,
                    "action": rule.action
                }
                for rule in self.get_rules()
            ],
            "notifications": [
                {
                    "id": notif.id,
                    "name": notif.name,
                    "description": notif.description,
                    "trigger_field": notif.trigger_field.name,
                    "trigger_condition": notif.trigger_condition,
                    "value": notif.value
                }
                for notif in self.get_notifications()
            ]
        }
        self.save()
