from django.db import models

class Topic(models.Model):
    name = models.Char<PERSON>ield(max_length=255, unique=True)
    
    class Meta:
        db_table = 'emails_topics'
        verbose_name = 'Topic'
        verbose_name_plural = 'Topics'
    
    def __str__(self):
        return self.name

class EmailTemplate(models.Model):
    name = models.Char<PERSON>ield(max_length=255)
    subject = models.CharField(max_length=255)
    body = models.TextField()
    topic = models.CharField(max_length=255, null=True, blank=True)
    email = models.JSONField(null=True, blank=True)
    is_external = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name
    
    def to_email_json(self):
        """
        Returns a dict with the name, subject, and body of the email template.
        """
        return {
            "name": self.name,
            "subject": self.subject,
            "body": self.body
        }
