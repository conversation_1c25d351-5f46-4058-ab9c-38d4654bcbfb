from django.core.management.base import BaseCommand
from Projects.models import ImplementationTypes

class Command(BaseCommand):
    help = 'Seeds the ImplementationTypes table with predefined elements'

    def handle(self, *args, **options):
        # Define the elements to insert (example data)
        predefined_fields = [
            {'name': 'Nueva'},
            {'name': 'Exist<PERSON>'},
            {'name': 'Migra<PERSON>'},
            {'name': 'Subrogación'}
        ]

        # Insert elements if they don't exist
        for field_data in predefined_fields:
            if not ImplementationTypes.objects.filter(name=field_data['name']).exists():
                ImplementationTypes.objects.create(**field_data)

        self.stdout.write(self.style.SUCCESS('✅ ImplementationTypes añadidos correctamente'))