from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

class CustomLoginView(APIView):
    swagger_tags = ['auth']

    @swagger_auto_schema(
        operation_summary="Login personalizado con email y contraseña",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'email': openapi.Schema(type=openapi.TYPE_STRING, description='Email del usuario'),
                'password': openapi.Schema(type=openapi.TYPE_STRING, description='Contraseña')
            },
            required=['email', 'password']
        ),
        responses={200: openapi.Response(description="Tokens JWT o aviso de cambio de contraseña")}
    )
    def post(self, request):
        email = request.data.get('email')
        password = request.data.get('password')

        user = authenticate(request, email=email, password=password)

        if user is None:
            return Response({"error": "Credenciales inválidas."}, status=status.HTTP_401_UNAUTHORIZED)

        refresh = RefreshToken.for_user(user)

        refresh = RefreshToken.for_user(user)

        response_data = {
            "refresh": str(refresh),
            "access": str(refresh.access_token),
            "must_change_password": user.must_change_password
        }

        if user.must_change_password:
            reset_token = user.generate_reset_password_token()
            response_data["reset_password_token"] = reset_token

        return Response(response_data, status=status.HTTP_200_OK)

class ChangePasswordView(APIView):
    permission_classes = [IsAuthenticated]
    swagger_tags = ['auth']

    @swagger_auto_schema(
        operation_summary="Cambiar contraseña del usuario autenticado",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'new_password': openapi.Schema(type=openapi.TYPE_STRING, description='Nueva contraseña')
            },
            required=['new_password']
        ),
        responses={200: "Contraseña cambiada correctamente"}
    )
    def post(self, request):
        user = request.user
        new_password = request.data.get('new_password')

        if not new_password:
            return Response({"error": "Nueva contraseña requerida."}, status=status.HTTP_400_BAD_REQUEST)

        user.set_password(new_password)
        user.must_change_password = False
        user.save()
        return Response({"message": "Contraseña cambiada correctamente."}, status=status.HTTP_200_OK)