from django.core.management.commands.migrate import Command as MigrateCommand
from django.apps import apps


class Command(MigrateCommand):
    def handle(self, *args, **options):
        super().handle(*args, **options)

        self.stdout.write(self.style.SUCCESS("✅ Ejecutando carga de fases tras migrate..."))

        Phase = apps.get_model('Fields', 'Phase')  # importante para evitar errores en tiempo de carga
        static_phases = [
            ("Start", 1),
            ("Collection", 2),
            ("Migration", 3),
            ("Test", 4),
            ("GoLive", 5),
            ("Incubadora", 6),
        ]

        for name, order in static_phases:
            Phase.objects.get_or_create(name=name, defaults={'order': order})

        self.stdout.write(self.style.SUCCESS("✅ Fases insertadas correctamente."))
