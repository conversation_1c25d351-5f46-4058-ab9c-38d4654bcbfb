from rest_framework.views import APIView
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework.permissions import IsAuthenticated
from Users.utilFunctions import permissions_required

from .models import Team

class TeamListView(APIView):
    """
    API endpoint to get all teams.
    """
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]
    @swagger_auto_schema(
        operation_summary="List all teams",
        responses={
            200: openapi.Response(
                description="List of teams",
                examples={
                    "application/json": [
                        {"id": 1, "name": "Team A", "code": "A"},
                        {"id": 2, "name": "Team B", "code": "B"}
                    ]
                }
            )
        }
    )
    def get(self, request):
        teams = Team.objects.all()
        data = [
            {"id": team.id, "name": team.name, "code": team.code}
            for team in teams
        ]
        return Response(data)

class ProduccionTeamsListView(APIView):
    """
    Lista todos los equipos cuyo nombre empieza por 'Producción'.
    """
    
    permission_classes = [IsAuthenticated, permissions_required('Users.visualizar_proyectos')]
    def get(self, request):
        teams = Team.objects.filter(name__startswith="Producción")
        data = [
            {
                "id": team.id,
                "name": team.name,
                "code": team.code
            }
            for team in teams
        ]
        return Response(data)


