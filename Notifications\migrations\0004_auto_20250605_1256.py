# Generated by Django 3.2.25 on 2025-06-05 10:56

from django.conf import settings
from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('Notifications', '0003_notification_is_external'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='notification',
            name='recipients',
        ),
        migrations.AddField(
            model_name='notification',
            name='all_recipients',
            field=models.ManyToManyField(related_name='all_notifications', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='notification',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='notification',
            name='leader_recipients',
            field=models.ManyToManyField(related_name='leader_notifications', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='notification',
            name='team_recipients',
            field=models.ManyToManyField(related_name='team_notifications', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='notification',
            name='user_recipients',
            field=models.ManyToManyField(related_name='user_notifications', to=settings.AUTH_USER_MODEL),
        ),
    ]
