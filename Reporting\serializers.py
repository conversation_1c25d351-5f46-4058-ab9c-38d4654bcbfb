from rest_framework import serializers
from .models import Report
from Fields.models import Field

class FieldSerializer(serializers.ModelSerializer):
    class Meta:
        model = Field
        fields = ['id', 'name']

class ReportSerializer(serializers.ModelSerializer):
    fields = FieldSerializer(many=True, read_only=True)

    class Meta:
        model = Report
        fields = [
            'id',
            'name',
            'name_en',
            'description',
            'description_en',
            'document_name',
            'document_name_en',
            'observations',
            'filters',
            'active',
            'phases',
            'subphases',
            'created_at',
            'fields',
        ]
