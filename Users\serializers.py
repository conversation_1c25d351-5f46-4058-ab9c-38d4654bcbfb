from rest_framework import serializers
from django.contrib.auth.models import Group, Permission
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework_simplejwt.tokens import RefreshToken

from .models import RMOUser

class GroupSerializer(serializers.ModelSerializer):
    permissions = serializers.SerializerMethodField()

    class Meta:
        model = Group
        fields = ['id', 'name', 'permissions']

    def get_permissions(self, obj):
        return PermissionSerializer(obj.permissions.all(), many=True).data

# Permission serializer
class PermissionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Permission
        fields = ['id', 'name', 'codename', 'content_type']

class UserSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField(read_only=True)
    groups = GroupSerializer(many=True)
    user_permissions = PermissionSerializer(many=True)

    class Meta:
        model = RMOUser
        fields = [
            'id', 'email', 'first_name', 'last_name', 'name',
            'position', 'team', 'team_code',
            'is_active', 'is_staff', 'is_superuser',
            'groups', 'user_permissions'
        ]
        read_only_fields = ['id', 'name', 'groups', 'user_permissions']

    def get_full_name(self, obj):
        first = obj.first_name or ''
        last = obj.last_name or ''
        return f"{first} {last}".strip() or obj.email  # fallback opcional: email si no hay nombre
        

    def get_name(self, obj):
        return f"{obj.name}"

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        request = self.context.get('request', None)
        user = request.user if request else None

        # Si no es admin, bloqueamos username y email
        if not (user and user.is_staff):
            # self.fields['username'].read_only = True
            self.fields['email'].read_only = True

class AssignUserRoleSerializer(serializers.Serializer):
    user_id = serializers.IntegerField()
    role_id = serializers.IntegerField()  # Changed from role_name to role_id

    def validate(self, data):
        try:
            data['user'] = RMOUser.objects.get(id=data['user_id'])
        except RMOUser.DoesNotExist:
            raise serializers.ValidationError("User not found.")

        try:
            data['group'] = Group.objects.get(id=data['role_id'])
        except Group.DoesNotExist:
            raise serializers.ValidationError("Role not found.")

        return data

    def save(self):
        user = self.validated_data['user']
        group = self.validated_data['group']

        # Remove old roles (optional)
        user.groups.clear()

        # Assign new role
        user.groups.add(group)
        user.save()
        return user
    

class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):

    def validate(self, attrs):
        data = super().validate(attrs)

        user = self.user

        must_change = getattr(user, 'must_change_password', False)
        data['must_change_password'] = must_change

        return data
